package mnms

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"

	"net/url"
	"os"
	"os/exec"
	"runtime"
	"sync"
	"testing"
	"time"

	"github.com/qeof/q"
)

func init() {
	q.O = "stderr"
	//q.P = ".*"
	QC.IsRoot = true
}
func TestCmdInfo(t *testing.T) {
	var jsonBytes []byte
	var err error
	cmdJson := `[{"all":true,"command":"scan snmp"},{"command":"scan gwd"},{"command":"msg syslog send 0 1 InsertDev new_device"},{"command":"beep 00-11-22-33-44-55"},{"command":"gwd beep 00-11-22-33-44-55"},{"command":"reset 00-11-22-33-44-55"},{"command":"gwd reset 00-11-22-33-44-55"},{"command":"config network set 00-11-22-33-44-55 ******* 3.3.3.3 ************* 0.0.0.0 switch 1 "},{"command":"gwd config network set 00-11-22-33-44-55 ********* ********* *********** 0.0.0.0 switch2"},{"command":"mtderase 00-11-22-33-44-55"},{"command":"gwd mtderase 00-11-22-33-44-55"},{"command":"snmp trap add 00-11-22-33-44-55 ******* 162 test-community"},{"command":"snmp trap del 00-11-22-33-44-55 ******* 162 test-community"},{"command":"snmp trap get 00-11-22-33-44-55"},{"command":"config save 00-11-22-33-44-55"},{"command":"snmp enable 00-11-22-33-44-55"},{"command":"snmp disable 00-11-22-33-44-55"},{"command":"snmp config syslog set 00-11-22-33-44-55 1 ******* 1234 7 1"},{"command":"snmp config syslog get 00-11-22-33-44-55"},{"command":"mqtt pub ************:1883 topictest testmesage"},{"command":"mqtt sub ************:1883 topictest"},{"command":"mqtt unsub ************:1883 topictest"},{"command":"mqtt list"},{"command":"config syslog set 00-11-22-33-44-55 1 ******* 1234 7 1"},{"command":"config user 00-11-22-33-44-55 user1 pass1"},{"command":"snmp get ************** *******.*******.0"},{"command":"snmp set ************** *******.*******.0 aaa123 OctetString"},{"command":"switch 00-60-E9-2D-91-3E show ip"},{"command": "snmp options 162 test 2c 5"},{"command":"config syslog get 00-11-22-33-44-55"},{"command":"firmware update 00-11-22-33-44-55 http://********/v1/files/xxx.dld"},{"command":"gwd firmware update 00-11-22-33-44-55 http://********/v1/files/xxx.dld"},{"command":"ssh tunnel close 2222"},{"command":"ssh tunnel fetch 2222"},{"command":"ssh tunnels list"},{"command":"opcua connect opc.tcp://127.0.0.1:4840"},{"command":"opcua read i=1002"},{"command":"opcua browse i=85"},{"command":"opcua sub i=1002"},{"command":"opcua deletesub 1 1"},{"command":"opcua close"},{"command":"wg config interface addresses set *********/32"},{"command":"wg config interface listenport set 51820"},{"command":"wg config interface mtu set 1420"},{"command":"wg config interface dns set *******"},{"command":"wg config interface preup add echo preup"},{"command":"wg config interface preup delete 0"},{"command":"wg config interface postup add echo_postup"},{"command":"wg config interface postup delete 0"},{"command":"wg config interface predown add echo_predown"},{"command":"wg config interface predown delete 0"},{"command":"wg config interface postdown add echo_postdown"},{"command":"wg config interface postdown delete 0"},{"command":"wg config interface set **********/32"},{"command":"wg config peer pubkey set 0 1234567890"},{"command":"wg config peer allowedips set 0 **********/32"},{"command":"wg config peer endpoint set ***************:55820"},{"command":"wg config peer persistentkeepalive set 0 25"},{"command":"wg config peer presharedkey set 0 1234567890"},{"command":"wg config peer add 1234567890 **********/24 ***************:55820 30"},{"command":"wg config peer delete 0"},{"command":"wg config generate"},{"command":"wg start"},{"command":"wg stop"}]`

	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		HTTPMain()
	}()
	_ = cleanMNMSConfig()
	_ = InitDefaultMNMSConfigIfNotExist()
	defer func() {
		_ = cleanMNMSConfig()
	}()
	myName := "test123"
	admintoken, err := GetToken("admin")
	if err != nil {
		t.Fatalf("get token %v", err)
	}
	q.Q("wait for root to become ready...")
	if err := waitForRoot(); err != nil {
		t.Fatal(err)
	}
	rooturl := fmt.Sprintf("http://localhost:%d/api/v1/register", QC.Port)
	ci := ClientInfo{Name: myName}
	jsonBytes, err = json.Marshal(ci)
	if err != nil {
		t.Fatalf("json marshal %v", err)
	}
	resp, err := PostWithToken(rooturl, admintoken, bytes.NewBuffer(jsonBytes))
	if err != nil {
		t.Fatalf("post %v", err)
	}
	if resp != nil {
		if resp.StatusCode != 200 {
			t.Fatalf("post StatusCode %d", resp.StatusCode)
		}
		q.Q(resp.Header)

		//save close
		resp.Body.Close()
	}

	rooturl = fmt.Sprintf("http://localhost:%d/api/v1/commands", QC.Port)

	resp, err = PostWithToken(rooturl, admintoken, bytes.NewBuffer([]byte(cmdJson)))
	if err != nil {
		t.Fatalf("post %v", err)
	}
	if resp != nil {
		t.Log(resp.Header)
		_, err := io.ReadAll(resp.Body)
		if err != nil {
			t.Fatalf("error: reading resp body %v", err)
		}
		// t.Log(string(body))
		//save close
		resp.Body.Close()
		if resp.StatusCode != 200 {
			t.Fatalf("post bad status %v", resp)
		}
	}

	rooturl = fmt.Sprintf("http://localhost:%d/api/v1/commands?cmd=all", QC.Port)
	resp, err = GetWithToken(rooturl, admintoken)

	if err != nil || resp.StatusCode != 200 {
		t.Fatalf("get %v", err)
	}
	if resp != nil {
		cmdinfo := make(map[string]CmdInfo)
		err := json.NewDecoder(resp.Body).Decode(&cmdinfo)
		if err != nil {
			t.Fatal(err)
		}
		q.Q("retrieved all cmd info", cmdinfo)
		//save close
		resp.Body.Close()
	}

	rooturl = fmt.Sprintf("http://localhost:%d/api/v1/commands?cmd=%s", QC.Port, url.QueryEscape("config user 00-11-22-33-44-55 user1 pass1"))
	resp, err = GetWithToken(rooturl, admintoken)

	if resp == nil {
		t.Fatalf("nil response")
	}
	if err != nil || resp.StatusCode != 200 {
		t.Fatalf("get %v", err)
	}

	cmdinfo := make(map[string]CmdInfo)
	err = json.NewDecoder(resp.Body).Decode(&cmdinfo)
	if err != nil {
		t.Fatal(err)
	}
	q.Q("config user", cmdinfo)
	//save close
	resp.Body.Close()

	// check snmp options
	rooturl = fmt.Sprintf("http://localhost:%d/api/v1/commands?cmd=%s", QC.Port, url.QueryEscape("snmp options 162 test 2c 5"))
	resp, err = GetWithToken(rooturl, admintoken)

	if resp == nil {
		t.Fatalf("nil response")
	}
	if err != nil || resp.StatusCode != 200 {
		t.Fatalf("get %v", err)
	}

	cmdinfo = make(map[string]CmdInfo)
	err = json.NewDecoder(resp.Body).Decode(&cmdinfo)
	if err != nil {
		t.Fatal(err)
	}
	q.Q("snmp options", cmdinfo)
	//save close
	resp.Body.Close()

	rooturl = fmt.Sprintf("http://localhost:%d/api/v1/commands?id=%s", QC.Port, url.QueryEscape(myName))
	resp, err = GetWithToken(rooturl, admintoken)

	if err != nil || resp == nil || resp.StatusCode != 200 {
		t.Fatalf("get %v", err)
	}

	cmdinfo = make(map[string]CmdInfo)
	err = json.NewDecoder(resp.Body).Decode(&cmdinfo)
	if err != nil {
		t.Fatal(err)
	}
	q.Q("id", cmdinfo)
	// save close
	resp.Body.Close()
}

func TestCmdSwitchCli(t *testing.T) {
	hostname, err := os.Hostname()
	if err != nil {
		t.Fatal()
	}
	if hostname == "testbed" {
		err := ReadTestData()
		if err != nil {
			t.Fatal(err)
		}
		//devId := "00-60-E9-21-2B-9E"
		devId := "00-60-E9-2D-91-3E"
		dev, err := FindDev(devId)
		if err != nil || dev == nil {
			t.Fatal(err)
		}

		if !CheckSwitchCliModel(dev.ModelName) {
			t.Fatalf("model %s does not support switch cli\n", dev.ModelName)
		}
		//show ip
		cmdinfo := CmdInfo{Command: "show ip"}
		_ = SendSwitch(&cmdinfo, dev, "admin", "default", "show ip", 1)
		q.Q(cmdinfo)
		//show informtion
		cmdinfo.Command = "show info"
		_ = SendSwitch(&cmdinfo, dev, "admin", "default", "show info", 1)
		q.Q(cmdinfo)
		//Save running configuration
		cmdinfo.Command = "config save"
		_ = SendSwitch(&cmdinfo, dev, "admin", "default", "copy running-config startup-config", 2) //EHG75xx,EHG76xx,EMGxxxx,RRHGxxxx
		q.Q(cmdinfo)
		//SNMP Enable
		cmdinfo.Command = "snmp enable"
		_ = SendSwitch(&cmdinfo, dev, "admin", "default", "snmp enable", 1)
		q.Q(cmdinfo)
		//SNMP Disable
		cmdinfo.Command = "snmp disable"
		_ = SendSwitch(&cmdinfo, dev, "admin", "default", "no snmp enable", 1)
		q.Q(cmdinfo)
		//Trap add
		cmdinfo.Command = "snmp trap add serverip community serverport"
		_ = SendSwitch(&cmdinfo, dev, "admin", "default", "snmp trap ******* test_community 162", 1)
		q.Q(cmdinfo)
		//Trap del
		cmdinfo.Command = "snmp trap del serverip community serverport"
		_ = SendSwitch(&cmdinfo, dev, "admin", "default", "no snmp trap ******* test_community 162", 1)
		q.Q(cmdinfo)
		//Trap get
		cmdinfo.Command = "show snmp trap"
		_ = SendSwitch(&cmdinfo, dev, "admin", "default", "show snmp trap", 1)
		q.Q(cmdinfo)
		fmt.Println(cmdinfo.Result)
	}
}

func TestBetterCmdSpacesInfo(t *testing.T) {
	var jsonBytes []byte
	var err error // all leading, trailing and between words middle spaces of commands
	cmdJson := `[{"command":"  beep   00-11-22-33-44-55  "},{"command":"  gwd   beep   00-11-22-33-44-55  "},{"command":"  reset   00-11-22-33-44-55  "},{"command":"  gwd    reset    00-11-22-33-44-55  "},{"command":"   config network   set 00-11-22-33-44-55 ******* ************* *******   "},{"command":"  gwd     config network set 00-11-22-33-44-55   ********* *********    *********** 0.0.0.0 switch2   "},{"command":"  mtderase   00-11-22-33-44-55   "},{"command":"   snmp   trap add 00-11-22-33-44-55   ******* 162 test-community   "},{"command":"   snmp trap del   00-11-22-33-44-55 *******   162 test-community   "},{"command":"  config   save 00-11-22-33-44-55   "},{"command":"  snmp enable    00-11-22-33-44-55  "},{"command":"   snmp disable   00-11-22-33-44-55   "},{"command":"   snmp    config syslog set 00-11-22-33-44-55 1 ******* 1234 7 1  "},{"command":"   snmp config syslog get    00-11-22-33-44-55   "},{"command":"   mqtt pub ************:1883    topictest testmesage   "},{"command":"   mqtt   sub ************:1883   topictest  "},{"command":"  mqtt   unsub ************:1883   topictest  "},{"command":"  mqtt   list  "},{"command":"   config   syslog set 00-11-22-33-44-55   1 ******* 1234 7 1   "},{"command":"   config user   00-11-22-33-44-55    user1 pass1   "},{"command":"   snmp get    ************** *******.*******.0  "},{"command":"   snmp set   ************** *******.*******.0 aaa123 OctetString  "},{"command":"   switch    00-60-E9-2D-91-3E show ip   "},{"command": "   snmp options 162    test 2c 5  "},{"command":"  config syslog get   00-11-22-33-44-55  "}]`

	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		HTTPMain()
	}()
	_ = cleanMNMSConfig()
	_ = InitDefaultMNMSConfigIfNotExist()
	defer func() {
		_ = cleanMNMSConfig()
	}()
	myName := "test123"
	admintoken, err := GetToken("admin")
	if err != nil {
		t.Fatalf("get token %v", err)
	}
	q.Q("wait for root to become ready...")
	if err := waitForRoot(); err != nil {
		t.Fatal(err)
	}
	rooturl := fmt.Sprintf("http://localhost:%d/api/v1/register", QC.Port)
	ci := ClientInfo{Name: myName}
	jsonBytes, err = json.Marshal(ci)
	if err != nil {
		t.Fatalf("json marshal %v", err)
	}
	resp, err := PostWithToken(rooturl, admintoken, bytes.NewBuffer(jsonBytes))
	if err != nil {
		t.Fatalf("post %v", err)
	}
	if resp != nil {
		if resp.StatusCode != 200 {
			t.Fatalf("post StatusCode %d", resp.StatusCode)
		}
		q.Q(resp.Header)

		//save close
		resp.Body.Close()
	}

	rooturl = fmt.Sprintf("http://localhost:%d/api/v1/commands", QC.Port)

	resp, err = PostWithToken(rooturl, admintoken, bytes.NewBuffer([]byte(cmdJson)))
	if err != nil {
		t.Fatalf("post %v", err)
	}
	if resp != nil {
		t.Log(resp.Header)
		_, err := io.ReadAll(resp.Body)
		if err != nil {
			t.Fatalf("error: reading resp body %v", err)
		}
		//t.Log(string(body))
		//save close
		resp.Body.Close()
		if resp.StatusCode != 200 {
			t.Fatalf("post bad status %v", resp)
		}
	}

	rooturl = fmt.Sprintf("http://localhost:%d/api/v1/commands?cmd=all", QC.Port)
	resp, err = GetWithToken(rooturl, admintoken)

	if err != nil || resp.StatusCode != 200 {
		t.Fatalf("get %v", err)
	}
	if resp != nil {
		cmdinfo := make(map[string]CmdInfo)
		err := json.NewDecoder(resp.Body).Decode(&cmdinfo)
		if err != nil {
			t.Fatal(err)
		}
		q.Q("retrieved all cmd info", cmdinfo)
		//save close
		resp.Body.Close()
	}

	rooturl = fmt.Sprintf("http://localhost:%d/api/v1/commands?cmd=%s", QC.Port, url.QueryEscape("config user 00-11-22-33-44-55 user1 pass1"))
	resp, err = GetWithToken(rooturl, admintoken)

	if resp == nil {
		t.Fatalf("nil response")
	}
	if err != nil || resp.StatusCode != 200 {
		t.Fatalf("get %v", err)
	}

	cmdinfo := make(map[string]CmdInfo)
	err = json.NewDecoder(resp.Body).Decode(&cmdinfo)
	if err != nil {
		t.Fatal(err)
	}
	q.Q("config user", cmdinfo)
	//save close
	resp.Body.Close()

	// check snmp options
	rooturl = fmt.Sprintf("http://localhost:%d/api/v1/commands?cmd=%s", QC.Port, url.QueryEscape("snmp options 162 test 2c 5"))
	resp, err = GetWithToken(rooturl, admintoken)

	if resp == nil {
		t.Fatalf("nil response")
	}
	if err != nil || resp.StatusCode != 200 {
		t.Fatalf("get %v", err)
	}

	cmdinfo = make(map[string]CmdInfo)
	err = json.NewDecoder(resp.Body).Decode(&cmdinfo)
	if err != nil {
		t.Fatal(err)
	}
	q.Q("snmp options", cmdinfo)
	//save close
	resp.Body.Close()

	rooturl = fmt.Sprintf("http://localhost:%d/api/v1/commands?id=%s", QC.Port, url.QueryEscape(myName))
	resp, err = GetWithToken(rooturl, admintoken)

	if err != nil || resp == nil || resp.StatusCode != 200 {
		t.Fatalf("get %v", err)
	}

	cmdinfo = make(map[string]CmdInfo)
	err = json.NewDecoder(resp.Body).Decode(&cmdinfo)
	if err != nil {
		t.Fatal(err)
	}
	q.Q("id", cmdinfo)
	// save close
	resp.Body.Close()
}

func TestNegativeNonExistingDevId(t *testing.T) {
	hostname, err := os.Hostname()
	if err != nil {
		t.Fatal()
	}
	if hostname == "testbed" {
		err := ReadTestData()
		if err != nil {
			t.Fatal(err)
		}
		//devId := "00-60-E9-21-2B-9E"
		devId := "00-60-E9-2D-91-3E"
		dev, err := FindDev(devId)
		if err != nil || dev == nil {
			t.Fatal(err)
		}
	}
}

func TestNegativeWrongCmdSyntax(t *testing.T) {
	var jsonBytes []byte
	var err error //command syntax errors
	cmdJson := `[{"command":"beeep 00-11-22-33-44-55"},{"command":"gwwd beep 00-11-22-33-44-55"},{"command":"reeset 00-11-22-33-44-55"},{"command":"ggwd reset 00-11-22-33-44-55"},{"command":"config network set 00-11-22-33-44-55 ******* ************* *******"},{"command":"mmtderase 00-11-22-33-44-55"},{"command":"snmpp trap add 00-11-22-33-44-55 ******* 162 test-community"},{"command":"snmp traap del 00-11-22-33-44-55 ******* 162 test-community"},{"command":"sconfig save 00-11-22-33-44-55"},{"command":"snmp enable 00-11-22-33-44-55"},{"command":"snmp disable 00-11-22-33-44-55"},{"command":"snmp configg syslog set 00-11-22-33-44-55 1 ******* 1234 7 1"},{"command":"snnmp config syslog get 00-11-22-33-44-55"},{"command":"configg syslog set 00-11-22-33-44-55 1 ******* 1234 7 1"},{"command":"config users 00-11-22-33-44-55 user1 pass1"},{"command":"snmp gett ************** *******.*******.0"},{"command":"ssnmp set ************** *******.*******.0 aaa123 OctetString"},{"command":"switch 00-60-E9-2D-91-3E show ip"},{"command":" mqqtt pub ************:1883  topictest testmesage"},{"command":"mmqtt sub ************:1883   topictest"},{"command":"mqttt unsub ************:1883 topictest"},{"command":" mqttt list"},{"command":"config ssyslog set 00-11-22-33-44-55   1 ******* 1234 7 1"},{"command":"configg users 00-11-22-33-44-55 user1 pass1"},{"command":"snmpp get ************** *******.*******.0 "},{"command":"snmp sset ************** *******.*******.0 aaa123 OctetString"},{"command":"sswitch 00-60-E9-2D-91-3E show ip"},{"command": "snmp options 162 test 2c 5"},{"command":"cconfig syslog get 00-11-22-33-44-55"}]`

	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		HTTPMain()
	}()
	_ = cleanMNMSConfig()
	_ = InitDefaultMNMSConfigIfNotExist()
	defer func() {
		_ = cleanMNMSConfig()
	}()
	myName := "test123"
	admintoken, err := GetToken("admin")
	if err != nil {
		t.Fatalf("get token %v", err)
	}
	q.Q("wait for root to become ready...")
	if err := waitForRoot(); err != nil {
		t.Fatal(err)
	}
	rooturl := fmt.Sprintf("http://localhost:%d/api/v1/register", QC.Port)
	ci := ClientInfo{Name: myName}
	jsonBytes, err = json.Marshal(ci)
	if err != nil {
		t.Fatalf("json marshal %v", err)
	}
	resp, err := PostWithToken(rooturl, admintoken, bytes.NewBuffer(jsonBytes))
	if err != nil {
		t.Fatalf("post %v", err)
	}
	if resp != nil {
		if resp.StatusCode != 200 {
			t.Fatalf("post StatusCode %d", resp.StatusCode)
		}
		q.Q(resp.Header)

		//save close
		resp.Body.Close()
	}

	rooturl = fmt.Sprintf("http://localhost:%d/api/v1/commands", QC.Port)

	resp, err = PostWithToken(rooturl, admintoken, bytes.NewBuffer([]byte(cmdJson)))
	if err != nil {
		t.Fatalf("post %v", err)
	}
	if resp != nil {
		t.Log(resp.Header)
		_, err := io.ReadAll(resp.Body)
		if err != nil {
			t.Fatalf("error: reading resp body %v", err)
		}
		//t.Log(string(body))
		//save close
		resp.Body.Close()
		if resp.StatusCode != 200 {
			t.Fatalf("post bad status %v", resp)
		}
	}

	rooturl = fmt.Sprintf("http://localhost:%d/api/v1/commands?cmd=all", QC.Port)
	resp, err = GetWithToken(rooturl, admintoken)

	if err != nil || resp.StatusCode != 200 {
		t.Fatalf("get %v", err)
	}
	if resp != nil {
		cmdinfo := make(map[string]CmdInfo)
		err := json.NewDecoder(resp.Body).Decode(&cmdinfo)
		if err != nil {
			t.Fatal(err)
		}
		q.Q("retrieved all cmd info", cmdinfo)
		//save close
		resp.Body.Close()
	}

	rooturl = fmt.Sprintf("http://localhost:%d/api/v1/commands?cmd=%s", QC.Port, url.QueryEscape("config user 00-11-22-33-44-55 user1 pass1"))
	resp, err = GetWithToken(rooturl, admintoken)

	if resp == nil {
		t.Fatalf("nil response")
	}
	if err != nil || resp.StatusCode != 200 {
		t.Fatalf("get %v", err)
	}

	cmdinfo := make(map[string]CmdInfo)
	err = json.NewDecoder(resp.Body).Decode(&cmdinfo)
	if err != nil {
		t.Fatal(err)
	}
	q.Q("config user", cmdinfo)
	//save close
	resp.Body.Close()

	// check snmp options
	rooturl = fmt.Sprintf("http://localhost:%d/api/v1/commands?cmd=%s", QC.Port, url.QueryEscape("snmp options 162 test 2c 5"))
	resp, err = GetWithToken(rooturl, admintoken)

	if resp == nil {
		t.Fatalf("nil response")
	}
	if err != nil || resp.StatusCode != 200 {
		t.Fatalf("get %v", err)
	}

	cmdinfo = make(map[string]CmdInfo)
	err = json.NewDecoder(resp.Body).Decode(&cmdinfo)
	if err != nil {
		t.Fatal(err)
	}
	q.Q("snmp options", cmdinfo)
	//save close
	resp.Body.Close()

	rooturl = fmt.Sprintf("http://localhost:%d/api/v1/commands?id=%s", QC.Port, url.QueryEscape(myName))
	resp, err = GetWithToken(rooturl, admintoken)

	if err != nil || resp == nil || resp.StatusCode != 200 {
		t.Fatalf("get %v", err)
	}

	cmdinfo = make(map[string]CmdInfo)
	err = json.NewDecoder(resp.Body).Decode(&cmdinfo)
	if err != nil {
		t.Fatal(err)
	}
	q.Q("id", cmdinfo)
	// save close
	resp.Body.Close()
}

func TestNegativeInvalidDevId(t *testing.T) {
	hostname, err := os.Hostname()
	if err != nil {
		t.Fatal()
	}
	if hostname == "testbed" {
		err := ReadTestData()
		if err != nil {
			t.Fatal(err)
		}
		//devId := "00-60-E9-21-2B-9G"
		devId := "00-60-E9-2D-91-3E"
		standardMac, err := IsValidMACAddress(devId)
		if err != nil {
			t.Fatal(err)
		}
		dev, err := FindDev(standardMac)
		if err != nil || dev == nil {
			t.Fatal(err)
		}
	}
}

func postDeviceListWithRootUrl(rootUrl string, token string, devInfo *DevInfo) error {
	// create devInfo map
	devinfoMap := make(map[string]DevInfo)
	// Add devices to client POST /api/v1/devices
	devinfoMap[devInfo.Mac] = *devInfo
	jsonBytes, err := json.Marshal(devinfoMap)
	if err != nil {
		return err
	}
	resp, err := PostWithToken(rootUrl+"/api/v1/devices", token, bytes.NewBuffer(jsonBytes))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	fmt.Println("Post device success")
	var devices map[string]DevInfo
	err = json.NewDecoder(resp.Body).Decode(&devices)
	if err != nil {
		fmt.Printf("Failed unmarshaling response: %s\n", err)
		return err
	}
	_, ok := devices["11-22-33-44-55-66"]
	if ok {
		delete(devices, "11-22-33-44-55-66")
	}
	return nil
}

func TestCmdAddClientService(t *testing.T) {
	// kill service
	killProcess()

	time.Sleep(5 * time.Second)
	// run service
	go func() {
		var cmd *exec.Cmd
		if runtime.GOOS == "windows" {
			cmd = exec.Command("./bbrootsvc/bbrootsvc.exe", "-n", "root")
		} else {
			cmd = exec.Command("./bbrootsvc/bbrootsvc", "-n", "root")
		}
		err := cmd.Run()
		if err != nil {
			t.Log("Close root service")
		}
	}()
	time.Sleep(5 * time.Second)
	go func() {
		var cmd *exec.Cmd
		if runtime.GOOS == "windows" {
			cmd = exec.Command("./bbnmssvc/bbnmssvc.exe", "-n", "client", "-r", "http://localhost:27182", "-rs", "localhost:5514")
		} else {
			cmd = exec.Command("./bbnmssvc/bbnmssvc", "-n", "client", "-r", "http://localhost:27182", "-rs", "localhost:5514")
		}
		err := cmd.Run()
		if err != nil {
			t.Log("Close network service")
		}
	}()
	time.Sleep(10 * time.Second)
	defer killProcess()

	RootURL := "http://localhost:27182"

	// login
	token, err := GetToken("admin")
	if err != nil {
		t.Fatalf("get token %v", err)
	}

	// Fake DevInfo
	devInfo := &DevInfo{
		Mac:            "00-AA-BB-33-44-55",
		ModelName:      "EHG7608",
		Timestamp:      "1629782400",
		Scanproto:      "gwd",
		IPAddress:      "***********",
		Netmask:        "*************",
		Gateway:        "************",
		Hostname:       "test_dev1",
		Kernel:         "4.14.180",
		Ap:             "1.0.0",
		ScannedBy:      "client1",
		ArpMissed:      0,
		Lock:           false,
		ReadCommunity:  "public",
		WriteCommunity: "private",
		IsDHCP:         false,
		TopologyProto:  "snmp",
	}

	err = postDeviceListWithRootUrl(RootURL, token, devInfo)
	if err != nil {
		t.Log(err)
	}

	// get command list
	cmdinfo := make(map[string]CmdInfo)
	resp, err := GetWithToken(RootURL+"/api/v1/commands?cmd=all", token)
	if err != nil || resp.StatusCode != 200 {
		t.Fatalf("get %v", err)
	}
	if resp != nil {
		err := json.NewDecoder(resp.Body).Decode(&cmdinfo)
		if err != nil {
			t.Fatal(err)
		}
		//t.Log("retrieved all cmd info", cmdinfo)
		resp.Body.Close()
	}
	originalCmdInfoLen := len(cmdinfo)

	commands := make([][]string, 0)
	commands = append(commands, []string{"beep", "00-AA-BB-33-44-55"})
	commands = append(commands, []string{"reset", "00-AA-BB-33-44-55"})

	for _, cmd := range commands {
		if runtime.GOOS == "windows" {
			err = exec.Command("./bbctl/bbctl.exe", cmd...).Run()
		} else {
			err = exec.Command("./bbctl/bbctl", cmd...).Run()
		}
		if err != nil {
			t.Log(err)
		}
		time.Sleep(100 * time.Millisecond)
	}

	time.Sleep(5 * time.Second)

	// get command list
	cmdinfoAgain := make(map[string]CmdInfo)
	resp, err = GetWithToken(RootURL+"/api/v1/commands?cmd=all", token)
	if err != nil || resp.StatusCode != 200 {
		t.Fatalf("get %v", err)
	}
	if resp != nil {
		err := json.NewDecoder(resp.Body).Decode(&cmdinfoAgain)
		if err != nil {
			t.Fatal(err)
		}
		//t.Log("retrieved all cmd info", cmdinfo)
		resp.Body.Close()
	}
	currentCmdInfoLen := len(cmdinfoAgain)

	// The number of commands cannot be increased or decreased
	t.Log(originalCmdInfoLen, currentCmdInfoLen, len(commands))
	if currentCmdInfoLen != (originalCmdInfoLen + len(commands)) {
		t.Fatal("Number of commands does not match")
	}
}

func TestFirstCmdWrong(t *testing.T) {
	// kill service
	killProcess()

	time.Sleep(5 * time.Second)
	// run service
	go func() {
		var cmd *exec.Cmd
		if runtime.GOOS == "windows" {
			cmd = exec.Command("./bbrootsvc/bbrootsvc.exe", "-n", "root")
		} else {
			cmd = exec.Command("./bbrootsvc/bbrootsvc", "-n", "root")
		}
		err := cmd.Run()
		if err != nil {
			t.Log("Close root service")
		}
	}()
	time.Sleep(5 * time.Second)
	go func() {
		var cmd *exec.Cmd
		if runtime.GOOS == "windows" {
			cmd = exec.Command("./bbnmssvc/bbnmssvc.exe", "-n", "client", "-r", "http://localhost:27182", "-rs", "localhost:5514")
		} else {
			cmd = exec.Command("./bbnmssvc/bbnmssvc", "-n", "client", "-r", "http://localhost:27182", "-rs", "localhost:5514")
		}
		err := cmd.Run()
		if err != nil {
			t.Log("Close network service")
		}
	}()
	time.Sleep(10 * time.Second)
	defer killProcess()

	RootURL := "http://localhost:27182"

	// login
	token, err := GetToken("admin")
	if err != nil {
		t.Fatalf("get token %v", err)
	}

	// Fake DevInfo
	devInfo := &DevInfo{
		Mac:            "00-AA-BB-33-44-55",
		ModelName:      "EHG7608",
		Timestamp:      "1629782400",
		Scanproto:      "gwd",
		IPAddress:      "***********",
		Netmask:        "*************",
		Gateway:        "************",
		Hostname:       "test_dev1",
		Kernel:         "4.14.180",
		Ap:             "1.0.0",
		ScannedBy:      "client1",
		ArpMissed:      0,
		Lock:           false,
		ReadCommunity:  "public",
		WriteCommunity: "private",
		IsDHCP:         false,
		TopologyProto:  "snmp",
	}

	err = postDeviceListWithRootUrl(RootURL, token, devInfo)
	if err != nil {
		t.Log(err)
	}

	// get command list
	cmdinfo := make(map[string]CmdInfo)
	resp, err := GetWithToken(RootURL+"/api/v1/commands?cmd=all", token)
	if err != nil || resp.StatusCode != 200 {
		t.Fatalf("get %v", err)
	}
	if resp != nil {
		err := json.NewDecoder(resp.Body).Decode(&cmdinfo)
		if err != nil {
			t.Fatal(err)
		}
		//t.Log("retrieved all cmd info", cmdinfo)
		resp.Body.Close()
	}
	originalCmdInfoLen := len(cmdinfo)

	commands := make([][]string, 0)
	commands = append(commands, []string{"beep", "00-AA-BB-33-44-55", "asdadad"})

	for _, cmd := range commands {
		if runtime.GOOS == "windows" {
			err = exec.Command("./bbctl/bbctl.exe", cmd...).Run()
		} else {
			err = exec.Command("./bbctl/bbctl", cmd...).Run()
		}
		if err != nil {
			t.Log(err)
		}
		time.Sleep(100 * time.Millisecond)
	}

	time.Sleep(5 * time.Second)

	// get command list
	cmdinfoAgain := make(map[string]CmdInfo)
	resp, err = GetWithToken(RootURL+"/api/v1/commands?cmd=all", token)
	if err != nil || resp.StatusCode != 200 {
		t.Fatalf("get %v", err)
	}
	if resp != nil {
		err := json.NewDecoder(resp.Body).Decode(&cmdinfoAgain)
		if err != nil {
			t.Fatal(err)
		}
		//t.Log("retrieved all cmd info", cmdinfo)
		resp.Body.Close()
	}
	currentCmdInfoLen := len(cmdinfoAgain)

	// The number of commands cannot be increased or decreased
	t.Log(originalCmdInfoLen, currentCmdInfoLen, len(commands))
	if currentCmdInfoLen != (originalCmdInfoLen + len(commands)) {
		t.Fatal("Number of commands does not match")
	}

}

/*
var test_mac = "11-22-33-44-55-66"

func injectTestDevice(mac string, capabilities map[string]bool) {
	QC.DevMutex.Lock()
	defer QC.DevMutex.Unlock()

	QC.DevData[mac] = DevInfo{
		ModelName:    "EHG1234",
		Mac:          mac,
		Capabilities: capabilities,
	}
}

// Helper function to clean up test device
func cleanupTestDevice(mac string) {
	QC.DevMutex.Lock()
	defer QC.DevMutex.Unlock()
	delete(QC.DevData, mac)
}

//	----------------------------------------Beep---------------------------------------------------
//
// Unit Test Cases
// Invalid Command Format
func TestBeepCmd_InvalidFormat(t *testing.T) {
	testCases := []struct {
		name    string
		command string
	}{
		{"No arguments", "beep"},
		{"Too many args", "beep 11-22-33-44-55-66 extra"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			cmdinfo := &CmdInfo{Command: tc.command}
			result, err := IsAValidBeepCmd(cmdinfo)

			assert.Error(t, err)
			assert.Contains(t, result.Status, "invalid command arguments")
		})
	}
}

// Invalid MAC Address
func TestBeepCmd_InvalidMAC(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "beep 11-GG-22-33-44-55"}
	result, err := IsAValidBeepCmd(cmdinfo)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid MAC Address")
	assert.Equal(t, cmdinfo, result) // Verify the returned cmdinfo is the same as input
}

// Unsupported Device Model
// This test case assumes that the device model "EHG2408" does not support the beep command
func TestBeepCmd_UnsupportedModel(t *testing.T) {
	// Setup test device in QC.DevData
	testDev := &DevInfo{
		Mac:       "11-22-33-44-55-66",
		ModelName: "EHG2408",
		Capabilities: map[string]bool{
			"agent": true,
		},
	}
	QC.DevMutex.Lock()
	QC.DevData[testDev.Mac] = *testDev
	QC.DevMutex.Unlock()

	defer func() {
		QC.DevMutex.Lock()
		delete(QC.DevData, testDev.Mac)
		QC.DevMutex.Unlock()
	}()

	cmdinfo := &CmdInfo{Command: "beep 11-22-33-44-55-66"}
	result, err := IsAValidBeepCmd(cmdinfo)
	//assert.Error(t, err)
	assert.Contains(t, err.Error(), "does not support beep")
	assert.Contains(t, result.Status, "does not support beep")
}

// Device With Agent Capability
func TestBeepCmd_WithAgent(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "beep 11-22-33-44-55-66", DevId: "11-22-33-44-55-66"}

	result, err := IsAValidBeepCmd(cmdinfo)
	assert.NoError(t, err)
	assert.Equal(t, "agent beep 11-22-33-44-55-66", result.Command)
}

// Device Without Agent Capability
func TestBeepCmd_WithoutAgent(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"gwd": true})
	defer cleanupTestDevice(test_mac)

	// Prepare command
	cmdinfo := &CmdInfo{
		Command: "beep 11-22-33-44-55-66",
		DevId:   "11-22-33-44-55-66",
	}

	// Call function under test
	result, err := IsAValidBeepCmd(cmdinfo)

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, "gwd beep 11-22-33-44-55-66", result.Command)
}

// Integration Test Cases
// 1. Happy Path - Successful Beep Command
func TestBeepCmd_Integration_Success(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "beep 11-22-33-44-55-66"}
	result, err := IsAValidBeepCmd(cmdinfo)

	assert.NoError(t, err)
	assert.Equal(t, "agent beep "+result.DevId, result.Command)
	assert.NotEmpty(t, result.DevId)
}

// Device Not Found
func TestBeepCmd_Integration_DeviceNotFound(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)
	cmdinfo := &CmdInfo{Command: "beep 11-22-33-44-55-77", DevId: "11-22-33-44-55-77"}
	result, err := IsAValidBeepCmd(cmdinfo)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "no such device")            // Matches actual error message
	assert.Contains(t, result.Status, "error: device not found") // Matches status string
}

// Database Error Scenario
func TestBeepCmd_DBError(t *testing.T) {
	// Backup original DevData
	originalDevData := QC.DevData
	QC.DevMutex.Lock()
	QC.DevData = nil // Simulate "database" error by nil'ing the map
	QC.DevMutex.Unlock()

	// Restore original data after test
	defer func() {
		QC.DevMutex.Lock()
		QC.DevData = originalDevData
		QC.DevMutex.Unlock()
	}()

	cmdinfo := &CmdInfo{Command: "beep 11-22-33-44-55-66"}
	result, err := IsAValidBeepCmd(cmdinfo)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "no such device") // Matches actual error message
	assert.Equal(t, cmdinfo, result)                  // Should return original cmdinfo
}

// Edge Cases

// 1.MAC Address Case Sensitivity
func TestBeepCmd_MACCaseSensitivity(t *testing.T) {
	testCases := []struct {
		inputMAC  string
		expectMAC string
	}{
		{"11:22:33:44:55:66", "11-22-33-44-55-66"}, // valid colon format
		{"11-22-33-44-55-66", "11-22-33-44-55-66"}, // already valid
	}

	for _, tc := range testCases {
		t.Run(tc.inputMAC, func(t *testing.T) {
			normalizedMAC := tc.expectMAC

			// Inject device into mock store
			QC.DevMutex.Lock()
			if QC.DevData == nil {
				QC.DevData = make(map[string]DevInfo)
			}
			QC.DevData[normalizedMAC] = DevInfo{
				ModelName: "EHG1234",
				Capabilities: map[string]bool{
					"agent": true,
				},
			}
			QC.DevMutex.Unlock()

			cmdinfo := &CmdInfo{Command: "beep " + tc.inputMAC}

			result, err := IsAValidBeepCmd(cmdinfo)
			assert.NoError(t, err)
			assert.Equal(t, "agent beep "+normalizedMAC, result.Command)
		})
	}
}

// ----------------------------------------Reboot---------------------------------------------------
var (
	// Original functions we'll mock
	findDevice              = FindDev
	insertAndPublish        = InsertAndPublishDevice
	originalCheckModel      = CheckSwitchCliModel
	originalSendSwitch      = SendSwitch
	convertSwitchCmd        = ConvertSwitchCmd
	originalCheckDeviceLock = CheckDeviceLock
)

// Unit Test Cases
// 1. Invalid Command Format
func TestResetCmd_InvalidFormat(t *testing.T) {
	testCases := []struct {
		name    string
		command string
	}{
		{"No arguments", "reset"},
		{"Too many args", "reset 11-22-33-44-55-66 extra"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			cmdinfo := &CmdInfo{Command: tc.command}
			result, err := IsAValidResetCmd(cmdinfo)

			assert.Error(t, err)
			assert.Contains(t, result.Status, "invalid command arguments")
		})
	}
}

// 2. Invalid MAC Address
func TestResetCmd_InvalidMAC(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)
	cmdinfo := &CmdInfo{Command: "reset invalid-mac"}
	result, err := IsAValidResetCmd(cmdinfo)

	assert.Error(t, err)
	assert.Contains(t, result.Status, "invalid MAC Address")
}

// 3. Device Not Found
func TestResetCmd_DeviceNotFound(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "reset 11-22-33-44-55-77", DevId: "11-22-33-44-55-77"}
	result, err := IsAValidResetCmd(cmdinfo)

	assert.Error(t, err)
	assert.Contains(t, result.Status, "device not found")
}

// 4. Device Locked
func TestResetCmd_DeviceLocked(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)
	cmdinfo := &CmdInfo{Command: "reset 11-22-33-44-55-66", DevId: "11-22-33-44-55-66"}
	// Mock CheckDeviceLock to simulate device lock
	originalCheckDeviceLock = func(mac string) error {
		return nil //let us assume device is unlocked
	}
	defer func() { originalCheckDeviceLock = CheckDeviceLock }()

	result, _ := IsAValidResetCmd(cmdinfo)

	if !strings.Contains(result.Command, "agent reset") {
		t.Errorf("Expected device lock error, got %s", result.Status)
	}
}

// 5. Reset With Agent Capability
func TestResetCmd_WithAgent(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "reset 11-22-33-44-55-66", DevId: "11-22-33-44-55-66"}
	result, err := IsAValidResetCmd(cmdinfo)

	assert.NoError(t, err)
	assert.True(t, strings.HasPrefix(result.Command, "agent reset "))
}

// 7. Reset Without Agent (Normal Case)
func TestResetCmd_WithoutAgent(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"gwd": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "reset 11-22-33-44-55-66", DevId: "11-22-33-44-55-66"}
	result, err := IsAValidResetCmd(cmdinfo)

	assert.NoError(t, err)
	assert.True(t, strings.HasPrefix(result.Command, "gwd reset "))
}
func TestResetCmd_EHG2408RequiresAgent(t *testing.T) {
	// Setup test device in QC.DevData
	testDev := &DevInfo{
		Mac:       "11-22-33-44-55-66",
		ModelName: "EHG2408",
		Capabilities: map[string]bool{
			"gwd": true,
		},
	}
	QC.DevMutex.Lock()
	QC.DevData[testDev.Mac] = *testDev
	QC.DevMutex.Unlock()

	defer func() {
		QC.DevMutex.Lock()
		delete(QC.DevData, testDev.Mac)
		QC.DevMutex.Unlock()
	}()

	cmdinfo := &CmdInfo{
		Command: "reset 11-22-33-44-55-66",
	}
	result, err := IsAValidResetCmd(cmdinfo)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "must required agent")
	assert.Contains(t, result.Status, "must required agent")
}

// Integration Test Cases
// 1. Successful Reset (Agent)
func TestResetCmd_Integration_AgentSuccess(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "reset 11-22-33-44-55-66"}
	result, err := IsAValidResetCmd(cmdinfo)

	assert.NoError(t, err)
	assert.True(t, strings.HasPrefix(result.Command, "agent reset "))
	assert.NotEmpty(t, result.DevId)
}

// 2. Successful Reset (GWD)
func TestResetCmd_Integration_GWDSuccess(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"gwd": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "reset 11-22-33-44-55-66"}
	result, err := IsAValidResetCmd(cmdinfo)

	assert.NoError(t, err)
	assert.True(t, strings.HasPrefix(result.Command, "gwd reset "))
	assert.NotEmpty(t, result.DevId)
}

// 3. Client Mismatch
func TestResetCmd_Integration_ClientMismatch(t *testing.T) {
	// Setup test device in QC.DevData
	testDev := &DevInfo{
		Mac:       "11-22-33-44-55-66",
		ModelName: "EHG2408",
		ScannedBy: "client1",
	}

	QC.DevMutex.Lock()
	QC.DevData[testDev.Mac] = *testDev
	QC.DevMutex.Unlock()

	defer func() {
		QC.DevMutex.Lock()
		delete(QC.DevData, testDev.Mac)
		QC.DevMutex.Unlock()
	}()

	cmdinfo := &CmdInfo{
		Command: "reset 11-22-33-44-55-66",
		Client:  "client2", // Different from scannedBy
	}
	result, err := IsAValidResetCmd(cmdinfo)

	assert.Error(t, err)
	assert.Contains(t, result.Status, "not scanned by the same client")
}

// ----------------------------------------Config save---------------------------------------------------
// Unit Test cases
func TestConfigSaveCmd_InvalidFormat(t *testing.T) {
	testCases := []struct {
		name    string
		command string
	}{
		{"No arguments", "config save"},
		{"Too many args", "config save 11-22-33-44-55-66 extra"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			cmdinfo := &CmdInfo{Command: tc.command}
			result, err := IsAValidConfigSaveCmd(cmdinfo)

			assert.Error(t, err)
			assert.Contains(t, result.Status, "invalid command arguments")
		})
	}
}

// 2. Invalid MAC Address
func TestConfigSaveCmd_InvalidMAC(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "config save invalid-mac"}
	result, err := IsAValidConfigSaveCmd(cmdinfo)

	assert.Error(t, err)
	assert.Contains(t, result.Status, "invalid MAC Address")
}

// 3. Device Not Found
func TestConfigSaveCmd_DeviceNotFound(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "config save 11-22-33-44-55-77", DevId: "11-22-33-44-55-77"}
	result, err := IsAValidConfigSaveCmd(cmdinfo)

	assert.Error(t, err)
	assert.Contains(t, result.Status, "device not found")
}

// 4. Agent Capability Success
func TestConfigSaveCmd_AgentSuccess(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "config save 11-22-33-44-55-66"}
	result, err := IsAValidConfigSaveCmd(cmdinfo)

	assert.NoError(t, err)
	assert.Equal(t, "agent config save "+result.DevId, result.Command)
	assert.Equal(t, "11-22-33-44-55-66", result.DevId)
}

// 5. Switch Command Success
func TestConfigSaveCmd_SwitchSuccess(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"gwd": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "config save 11-22-33-44-55-66"}
	result, err := IsAValidConfigSaveCmd(cmdinfo)

	assert.NoError(t, err)
	assert.Equal(t, "switch config save "+result.DevId, result.Command)
	assert.NotEmpty(t, result.Timestamp)
}

// 6. Special Case EHG2408
func TestConfigSaveCmd_EHG2408RequiresAgent(t *testing.T) {
	// Setup test device in QC.DevData
	testDev := &DevInfo{
		Mac:       "11-22-33-44-55-66",
		ModelName: "EHG2408",
		Capabilities: map[string]bool{
			"gwd": true,
		},
	}
	QC.DevMutex.Lock()
	QC.DevData[testDev.Mac] = *testDev
	QC.DevMutex.Unlock()

	defer func() {
		QC.DevMutex.Lock()
		delete(QC.DevData, testDev.Mac)
		QC.DevMutex.Unlock()
	}()

	cmdinfo := &CmdInfo{
		Command: "config save 11-22-33-44-55-66",
	}
	result, err := IsAValidConfigSaveCmd(cmdinfo)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "must required agent")
	assert.Contains(t, result.Status, "must required agent")
}

// Integration Test Cases
// 1. Successful Agent Config Save
func TestConfigSaveCmd_Integration_AgentSuccess(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "config save 11-22-33-44-55-66"}
	result, err := IsAValidConfigSaveCmd(cmdinfo)

	assert.NoError(t, err)
	assert.Equal(t, "agent config save "+result.DevId, result.Command)
	assert.Equal(t, "11-22-33-44-55-66", result.DevId)
}

// 2. Successful Switch Config Save
func TestConfigSaveCmd_Integration_SwitchSuccess(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"gwd": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "config save 11-22-33-44-55-66"}
	result, err := IsAValidConfigSaveCmd(cmdinfo)

	assert.NoError(t, err)
	assert.Equal(t, "switch config save "+result.DevId, result.Command)
}

// ----------------------------------------Config user Authentication---------------------------------------------------
// Unit Test cases
// 1. Invalid Command Format
func TestConfigUserCmd_InvalidArguments(t *testing.T) {
	testCases := []struct {
		name    string
		command string
	}{
		{"Missing args", "config user"},
		{"Incomplete args", "config user 11-22-33-44-55-66 username"},
		{"Extra args", "config user 11-22-33-44-55-66 username password extra"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			cmdinfo := &CmdInfo{Command: tc.command}
			result, err := IsAValidConfigUserCmd(cmdinfo)

			assert.Error(t, err)
			assert.Contains(t, result.Status, "invalid command arguments")
		})
	}
}

// 2. Invalid MAC Address
func TestConfigUserCmd_InvalidMAC(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "config user invalid-mac username password"}
	result, err := IsAValidConfigUserCmd(cmdinfo)

	assert.Error(t, err)
	assert.Contains(t, result.Status, "invalid MAC Address")
}

// 3. Invalid Username
func TestConfigUserCmd_ShortUsername(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "config user 11-22-33-44-55-66 user password", DevId: test_mac}
	result, _ := IsAValidConfigUserCmd(cmdinfo)
	assert.Contains(t, result.Status, "username must be more than 5 characters")
}

// 4. Invalid Password
func TestConfigUserCmd_ShortPassword(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "config user 11-22-33-44-55-66 username pass", DevId: test_mac}
	result, _ := IsAValidConfigUserCmd(cmdinfo)

	assert.Contains(t, result.Status, "password must be more than 7 characters")
}

// 5. Valid Command
func TestConfigUserCmd_Valid(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "config user 11-22-33-44-55-66 username password"}
	result, err := IsAValidConfigUserCmd(cmdinfo)

	assert.NoError(t, err)
	assert.Equal(t, result.Command, "config user 11-22-33-44-55-66 username password")
}

// 6. Device Not Found
func TestConfigUserCmd_DeviceNotFound(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "config user 11-22-33-44-55-77 username password"}
	result, err := IsAValidConfigUserCmd(cmdinfo)

	assert.Error(t, err)
	assert.Contains(t, result.Status, "device not found")
}

// 7. Successful Authentication
func TestConfigUserAuthCmd_Success(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	// Backup originals
	originalFind := findDevice
	originalInsert := insertAndPublish

	insertCalled := true
	insertAndPublish = func(dev DevInfo) {
		assert.Equal(t, "newuser", dev.UserName)
		assert.Equal(t, "newpass123", dev.PassWord)
		insertCalled = true
	}

	// Restore after test
	defer func() {
		findDevice = originalFind
		insertAndPublish = originalInsert
	}()

	cmdinfo := &CmdInfo{
		Command: "config user 11-22-33-44-55-66 newuser newpass123",
		DevId:   "11-22-33-44-55-66",
	}
	result := ConfigUserAuthCmd(cmdinfo)

	assert.Equal(t, "ok", result.Status)
	assert.True(t, insertCalled)
}

// Integration Tests
// 1. Full Successful Flow
func TestConfigUser_Integration_Success(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	// First validate the command
	cmdinfo := &CmdInfo{Command: "config user 11-22-33-44-55-66 validuser validpass123"}
	result, err := IsAValidConfigUserCmd(cmdinfo)
	assert.NoError(t, err)

	// Then authenticate
	result = ConfigUserAuthCmd(result)
	assert.Equal(t, result.Command, "config user 11-22-33-44-55-66 validuser validpass123")
	assert.Equal(t, result.DevId, "11-22-33-44-55-66")
	assert.Equal(t, "ok", result.Status)

}

// ----------------------------------------MtdErase---------------------------------------------------
// 1. Unit Test cases
func TestMtdEraseCmd_InvalidFormat(t *testing.T) {
	testCases := []struct {
		name    string
		command string
	}{
		{"No arguments", "mtderase"},
		{"Too many args", "mtderase 11:22:33:44:55:66 extra"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			cmdinfo := &CmdInfo{Command: tc.command}
			result, err := IsAValidMtdEraseCmd(cmdinfo)

			assert.Error(t, err)
			assert.Contains(t, result.Status, "invalid command arguments")
		})
	}
}

// 2. Invalid MAC Address
func TestMtdEraseCmd_InvalidMAC(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "mtderase invalid-mac"}
	result, err := IsAValidMtdEraseCmd(cmdinfo)

	assert.Error(t, err)
	assert.Contains(t, result.Status, "invalid MAC Address")
}

// 3. Device Not Found
func TestMtdEraseCmd_DeviceNotFound(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "mtderase 11:22:33:44:55:77"}
	result, err := IsAValidMtdEraseCmd(cmdinfo)

	assert.Error(t, err)
	assert.Contains(t, result.Status, "device not found")
}

// 4. Device Lock Check Failure
func TestMtdEraseCmd_DeviceLocked(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	// Mock CheckDeviceLock to simulate device unlock
	originalCheckDeviceLock = func(mac string) error {
		return nil //let us assume device is unlocked
	}
	defer func() { originalCheckDeviceLock = CheckDeviceLock }()

	cmdinfo := &CmdInfo{Command: "mtderase 11-22-33-44-55-66", DevId: "11-22-33-44-55-66"}
	result, _ := IsAValidMtdEraseCmd(cmdinfo)

	if !strings.Contains(result.Command, "agent mtderase") {
		t.Errorf("Expected device lock error, got %s", result.Status)
	}
}

// 5. MtdErase With Agent Capability
func TestMtdEraseCmd_WithAgent(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "mtderase 11:22:33:44:55:66", DevId: test_mac}
	result, err := IsAValidMtdEraseCmd(cmdinfo)

	assert.NoError(t, err)
	assert.True(t, strings.HasPrefix(result.Command, "agent mtderase "))
}

// 6. MtdErase Without Agent (EHG2408 Special Case)
func TestMtdEraseCmd_EHG2408RequiresAgent(t *testing.T) {
	// Setup test device in QC.DevData
	testDev := &DevInfo{
		Mac:       "11-22-33-44-55-66",
		ModelName: "EHG2408",
		Capabilities: map[string]bool{
			"gwd": true,
		},
	}
	QC.DevMutex.Lock()
	QC.DevData[testDev.Mac] = *testDev
	QC.DevMutex.Unlock()

	defer func() {
		QC.DevMutex.Lock()
		delete(QC.DevData, testDev.Mac)
		QC.DevMutex.Unlock()
	}()

	cmdinfo := &CmdInfo{
		Command: "mtderase 11-22-33-44-55-66",
	}
	result, err := IsAValidMtdEraseCmd(cmdinfo)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "must required agent")
	assert.Contains(t, result.Status, "must required agent")
}

// 7. MtdErase Without Agent (Normal Case)
func TestMtdEraseCmd_WithoutAgent(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"gwd": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "mtderase 11:22:33:44:55:66"}
	result, err := IsAValidMtdEraseCmd(cmdinfo)

	assert.NoError(t, err)
	assert.True(t, strings.HasPrefix(result.Command, "gwd mtderase"))
}

// Integration Test Cases
// 1. Successful MtdErase (Agent)
func TestMtdEraseCmd_Integration_AgentSuccess(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "mtderase 11:22:33:44:55:66", DevId: test_mac}
	result, err := IsAValidMtdEraseCmd(cmdinfo)

	assert.NoError(t, err)
	assert.True(t, strings.HasPrefix(result.Command, "agent mtderase "))
}

// 2. Successful MtdErase (GWD)
func TestMtdEraseCmd_Integration_GWDSuccess(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"gwd": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "mtderase 11:22:33:44:55:66"}
	result, err := IsAValidMtdEraseCmd(cmdinfo)

	assert.NoError(t, err)
	assert.True(t, strings.HasPrefix(result.Command, "gwd mtderase "))
}

// 3. Client Mismatch
func TestMtdEraseCmd_Integration_ClientMismatch(t *testing.T) {
	// Setup test device in QC.DevData
	testDev := &DevInfo{
		Mac:       "11-22-33-44-55-66",
		ModelName: "EHG1234",
		ScannedBy: "client1",
	}

	QC.DevMutex.Lock()
	QC.DevData[testDev.Mac] = *testDev
	QC.DevMutex.Unlock()

	defer func() {
		QC.DevMutex.Lock()
		delete(QC.DevData, testDev.Mac)
		QC.DevMutex.Unlock()
	}()

	cmdinfo := &CmdInfo{
		Command: "mtderase 11:22:33:44:55:66",
		Client:  "client2", // Different from scannedBy
	}
	result, err := IsAValidMtdEraseCmd(cmdinfo)

	assert.Error(t, err)
	assert.Contains(t, result.Status, "not scanned by the same client")
}

// ----------------------------------------Network setting---------------------------------------------------
// Unit Test Cases
// 1. Invalid Command Format
func TestConfigNetworkCmd_InvalidArguments(t *testing.T) {
	testCases := []struct {
		name    string
		command string
	}{
		{"Missing args", "config network set"},
		{"Partial args", "config network set 11-22-33-44-55-66 *************"},
		{"Extra args", "config network set 11-22-33-44-55-66 ************* ************* ************* *********** hostname 0 extra"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			cmdinfo := &CmdInfo{Command: tc.command}
			result, err := IsAValidConfigNetworkCmd(cmdinfo)

			assert.Error(t, err)
			assert.Contains(t, result.Status, "invalid command arguments")
		})
	}
}

// 2. Invalid MAC Address
func TestConfigNetworkCmd_InvalidMAC(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "config network set invalid-mac ************* ************* ************* *********** hostname 0"}
	result, err := IsAValidConfigNetworkCmd(cmdinfo)

	assert.Error(t, err)
	assert.Contains(t, result.Status, "invalid MAC Address")
}

// 3. Device Lock Check Failure
func TestConfigNetworkCmd_DeviceLocked(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)
	// Mock CheckDeviceLock to simulate device unlock
	originalCheckDeviceLock = func(mac string) error {
		return nil //let us assume device is unlocked
	}
	defer func() { originalCheckDeviceLock = CheckDeviceLock }()

	cmdinfo := &CmdInfo{Command: "config network set 11-22-33-44-55-66 ************* ************* ************* *********** hostname 0"}
	result, _ := IsAValidConfigNetworkCmd(cmdinfo)

	if !strings.Contains(result.Command, "agent config network set") {
		t.Errorf("Expected device lock error, got %s", result.Status)
	}
}

// 4. Invalid IP Addresses
func TestConfigNetworkCmd_InvalidIPs(t *testing.T) {
	testCases := []struct {
		name     string
		command  string
		expected string
	}{
		{
			"Invalid current IP",
			"config network set 11-22-33-44-55-66 invalid.ip ************* ************* *********** hostname 0",
			"error: IP Address: invalid.ip - Invalid",
		},
		{
			"Invalid new IP",
			"config network set 11-22-33-44-55-66 ************* invalid.ip ************* *********** hostname 0",
			"error: IP Address: invalid.ip - Invalid",
		},
		{
			"Invalid mask",
			"config network set 11-22-33-44-55-66 ************* ************* invalid.mask *********** hostname 0",
			"error: IP Address: invalid.mask - Invalid",
		},
		{
			"Invalid gateway",
			"config network set 11-22-33-44-55-66 ************* ************* ************* invalid.gateway hostname 0",
			"error: IP Address: invalid.gateway - Invalid",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Inject device into mock store
			injectTestDevice(test_mac, map[string]bool{"agent": true})
			defer cleanupTestDevice(test_mac)
			cmdinfo := &CmdInfo{Command: tc.command}
			result, err := IsAValidConfigNetworkCmd(cmdinfo)

			assert.Error(t, err)
			assert.Equal(t, tc.expected, result.Status)
		})
	}
}

// 5. Invalid DHCP Value
func TestConfigNetworkCmd_InvalidDHCP(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "config network set 11-22-33-44-55-66 ************* ************* ************* *********** hostname 2"}
	result, _ := IsAValidConfigNetworkCmd(cmdinfo)

	assert.Contains(t, result.Status, "invalid DHCP value")
}

// 6. Agent Capability Success
func TestConfigNetworkCmd_AgentSuccess(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "config network set 11-22-33-44-55-66 ************* ************* ************* *********** hostname 1"}
	result, err := IsAValidConfigNetworkCmd(cmdinfo)

	assert.NoError(t, err)
	assert.True(t, strings.HasPrefix(result.Command, "agent config network set"))
	assert.Contains(t, result.Command, "*************")
	assert.Contains(t, result.Command, "hostname")
	assert.Contains(t, result.Command, "1")
}

// 7. GWD Command Success
func TestConfigNetworkCmd_GWDSuccess(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"gwd": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "config network set 11-22-33-44-55-66 ************* ************* ************* *********** hostname 0"}
	result, err := IsAValidConfigNetworkCmd(cmdinfo)

	assert.NoError(t, err)
	assert.True(t, strings.HasPrefix(result.Command, "gwd config network set"))
	assert.Contains(t, result.Command, "*************")
	assert.Contains(t, result.Command, "*************")
}

// 8. EHG2408 Special Case
func TestConfigNetworkCmd_EHG2408RequiresAgent(t *testing.T) {
	// Setup test device in QC.DevData
	testDev := &DevInfo{
		Mac:       "11-22-33-44-55-66",
		ModelName: "EHG2408",
		Capabilities: map[string]bool{
			"gwd": true,
		},
	}
	QC.DevMutex.Lock()
	QC.DevData[testDev.Mac] = *testDev
	QC.DevMutex.Unlock()

	defer func() {
		QC.DevMutex.Lock()
		delete(QC.DevData, testDev.Mac)
		QC.DevMutex.Unlock()
	}()

	cmdinfo := &CmdInfo{Command: "config network set 11-22-33-44-55-66 ************* ************* ************* *********** hostname 0"}
	result, err := IsAValidConfigNetworkCmd(cmdinfo)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "must required agent")
	assert.Contains(t, result.Status, "must required agent")
}

// Integration Test Cases
// 1. Full Agent Success Flow
func TestConfigNetworkCmd_Integration_AgentSuccess(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "config network set 11-22-33-44-55-66 ************* ************* ************* *********** hostname 1"}
	result, err := IsAValidConfigNetworkCmd(cmdinfo)

	assert.NoError(t, err)
	assert.Equal(t, "agent config network set 11-22-33-44-55-66 ************* ************* *********** hostname 1", result.Command)
}

//2. Full GWD Success Flow

func TestConfigNetworkCmd_Integration_GWDSuccess(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"gwd": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "config network set 11-22-33-44-55-66 ************* ************* ************* *********** hostname 0"}
	result, err := IsAValidConfigNetworkCmd(cmdinfo)

	assert.NoError(t, err)
	assert.Equal(t, "gwd config network set 11-22-33-44-55-66 ************* ************* ************* *********** hostname", result.Command)
}

// ----------------------------------------Syslog Configuration Setting---------------------------------------------------
// 1. Unit Test Cases
// Command Validation
func TestConfigSyslogSetCmd_ArgumentValidation(t *testing.T) {
	testCases := []struct {
		name     string
		command  string
		expected string
	}{
		{
			name:     "Valid command",
			command:  "config syslog set 11-22-33-44-55-66 1 ************* 514 6 1",
			expected: "",
		},
		{
			name:     "Missing arguments",
			command:  "config syslog set 11-22-33-44-55-66 1 ************* 514",
			expected: "error: invalid command arguments, expected 9 but got 7",
		},
		{
			name:     "Extra arguments",
			command:  "config syslog set 11-22-33-44-55-66 1 ************* 514 6 1 extra",
			expected: "error: invalid command arguments, expected 9 but got 10",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Inject device into mock store
			injectTestDevice(test_mac, map[string]bool{"agent": true})
			defer cleanupTestDevice(test_mac)
			cmdinfo := &CmdInfo{Command: tc.command}
			_, err := IsAValidConfigSyslogSetCmd(cmdinfo)

			if tc.expected == "" {
				assert.NoError(t, err)
			} else {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.expected)
			}
		})
	}
}

// Status Validation
func TestConfigSyslogSetCmd_StatusValidation(t *testing.T) {
	testCases := []struct {
		status string
		valid  bool
	}{
		{"0", true},
		{"1", true},
		{"2", false},
		{"enable", false},
		{"", false},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("Status_%s", tc.status), func(t *testing.T) {
			// Inject device into mock store
			injectTestDevice(test_mac, map[string]bool{"agent": true})
			defer cleanupTestDevice(test_mac)
			cmd := fmt.Sprintf("config syslog set 11-22-33-44-55-66 %s ************* 514 6 1", tc.status)
			cmdinfo := &CmdInfo{Command: cmd}
			_, err := IsAValidConfigSyslogSetCmd(cmdinfo)

			if tc.valid {
				assert.NoError(t, err)
			} else {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "invalid syslog status value")
			}
		})
	}
}

// IP Address Validation
func TestConfigSyslogSetCmd_IPValidation(t *testing.T) {
	testCases := []struct {
		ip    string
		valid bool
	}{
		{"***********", true},
		{"0.0.0.0", true},
		{"***************", true},
		{"256.168.1.1", false},
		{"192.168.1", false},
		{"invalid.ip", false},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("IP_%s", tc.ip), func(t *testing.T) {
			// Inject device into mock store
			injectTestDevice(test_mac, map[string]bool{"agent": true})
			defer cleanupTestDevice(test_mac)
			cmd := fmt.Sprintf("config syslog set 11-22-33-44-55-66 1 %s 514 6 1", tc.ip)
			cmdinfo := &CmdInfo{Command: cmd}
			_, err := IsAValidConfigSyslogSetCmd(cmdinfo)

			if tc.valid {
				assert.NoError(t, err)
			} else {
				assert.Error(t, err)
				expectedError := fmt.Sprintf("IP Address: %s - Invalid", tc.ip)
				assert.Contains(t, err.Error(), expectedError)
			}
		})
	}
}

// Port Validation
func TestConfigSyslogSetCmd_PortValidation(t *testing.T) {
	testCases := []struct {
		port  string
		valid bool
	}{
		{"514", true},      // Standard syslog port
		{"1514", true},     // Valid high port
		{"1", true},        // Minimum valid port
		{"65535", true},    // Maximum valid port
		{"0", false},       // Invalid port
		{"65536", false},   // Port too high
		{"invalid", false}, // Non-numeric
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("Port_%s", tc.port), func(t *testing.T) {
			// Inject device into mock store
			injectTestDevice(test_mac, map[string]bool{"agent": true})
			defer cleanupTestDevice(test_mac)
			cmd := fmt.Sprintf("config syslog set 11-22-33-44-55-66 1 ************* %s 6 1", tc.port)
			cmdinfo := &CmdInfo{Command: cmd}
			_, err := IsAValidConfigSyslogSetCmd(cmdinfo)

			if tc.valid {
				assert.NoError(t, err)
			} else {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "invalid server port")
			}
		})
	}
}

// Log Level Validation
func TestConfigSyslogSetCmd_LogLevelValidation(t *testing.T) {
	testCases := []struct {
		level string
		valid bool
	}{
		{"0", true},      // Emergency
		{"3", true},      // Error
		{"6", true},      // Informational
		{"7", true},      // Debug
		{"-1", false},    // Invalid
		{"8", false},     // Invalid
		{"debug", false}, // Not numeric
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("Level_%s", tc.level), func(t *testing.T) {
			// Inject device into mock store
			injectTestDevice(test_mac, map[string]bool{"agent": true})
			defer cleanupTestDevice(test_mac)
			cmd := fmt.Sprintf("config syslog set 11-22-33-44-55-66 1 ************* 514 %s 1", tc.level)
			cmdinfo := &CmdInfo{Command: cmd}
			_, err := IsAValidConfigSyslogSetCmd(cmdinfo)

			if tc.valid {
				assert.NoError(t, err)
			} else {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "invalid log level")
			}
		})
	}
}

// LogToFlash Validation
func TestConfigSyslogSetCmd_LogToFlashValidation(t *testing.T) {
	testCases := []struct {
		value string
		valid bool
	}{
		{"0", true},
		{"1", true},
		{"2", false},
		{"true", false},
		{"", false},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("LogToFlash_%s", tc.value), func(t *testing.T) {
			// Inject device into mock store
			injectTestDevice(test_mac, map[string]bool{"agent": true})
			defer cleanupTestDevice(test_mac)
			cmd := fmt.Sprintf("config syslog set 11-22-33-44-55-66 1 ************* 514 6 %s", tc.value)
			cmdinfo := &CmdInfo{Command: cmd}
			_, err := IsAValidConfigSyslogSetCmd(cmdinfo)

			if tc.valid {
				assert.NoError(t, err)
			} else {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "invalid LogToFlash value")
			}
		})
	}
}

// 2. Integration Test Cases
// Agent Capability Path
func TestConfigSyslogSetCmd_AgentPath(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "config syslog set 11-22-33-44-55-66 1 ************* 514 6 1"}
	result, err := IsAValidConfigSyslogSetCmd(cmdinfo)

	assert.NoError(t, err)
	assert.Equal(t, "agent config syslog set 11-22-33-44-55-66 1 ************* 514 6 1", result.Command)
}

// SNMP Capability Path
func TestConfigSyslogSetCmd_SNMPPath(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"gwd": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "config syslog set 11-22-33-44-55-66 1 ************* 514 6 1"}
	result, err := IsAValidConfigSyslogSetCmd(cmdinfo)

	assert.NoError(t, err)
	assert.Equal(t, "snmp config syslog set 11-22-33-44-55-66 1 ************* 514 6 1", result.Command)
}

// EHG2408 Special Case
func TestConfigSyslogSetCmd_EHG2408RequiresAgent(t *testing.T) {
	// Setup test device in QC.DevData
	testDev := &DevInfo{
		Mac:       "11-22-33-44-55-66",
		ModelName: "EHG2408",
		Capabilities: map[string]bool{
			"gwd": true,
		},
	}
	QC.DevMutex.Lock()
	QC.DevData[testDev.Mac] = *testDev
	QC.DevMutex.Unlock()

	defer func() {
		QC.DevMutex.Lock()
		delete(QC.DevData, testDev.Mac)
		QC.DevMutex.Unlock()
	}()

	cmdinfo := &CmdInfo{Command: "config syslog set 11-22-33-44-55-66 1 ************* 514 6 1"}
	result, err := IsAValidConfigSyslogSetCmd(cmdinfo)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "must required agent")
	assert.Contains(t, result.Status, "must required agent")
}

// 3. Edge Case Tests
// Empty Fields
func TestConfigSyslogSetCmd_EmptyFields(t *testing.T) {
	testCases := []struct {
		field string
		value string
	}{
		{"MAC", ""},
		{"Status", ""},
		{"IP", ""},
		{"Port", ""},
		{"Level", ""},
		{"LogToFlash", ""},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("Empty_%s", tc.field), func(t *testing.T) {
			// Inject device into mock store
			injectTestDevice(test_mac, map[string]bool{"agent": true})
			defer cleanupTestDevice(test_mac)
			cmd := "config syslog set 11-22-33-44-55-66 1 ************* 514 6 1"
			parts := strings.Split(cmd, " ")
			// Replace the appropriate part with empty string
			switch tc.field {
			case "MAC":
				parts[3] = ""
			case "Status":
				parts[4] = ""
			case "IP":
				parts[5] = ""
			case "Port":
				parts[6] = ""
			case "Level":
				parts[7] = ""
			case "LogToFlash":
				parts[8] = ""
			}
			cmd = strings.Join(parts, " ")

			cmdinfo := &CmdInfo{Command: cmd}
			_, err := IsAValidConfigSyslogSetCmd(cmdinfo)
			assert.Error(t, err)
		})
	}
}

// 4. Security Tests
// Command Injection Attempts
func TestConfigSyslogSetCmd_CommandInjection(t *testing.T) {
	testCases := []struct {
		input string
		field string
	}{
		{"11-22-33-44-55-66; rm -rf /", "MAC"},
		{"1; shutdown", "Status"},
		{"*************; ls", "IP"},
		{"514; cat /etc/passwd", "Port"},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("Injection_%s", tc.field), func(t *testing.T) {
			// Inject device into mock store
			injectTestDevice(test_mac, map[string]bool{"agent": true})
			defer cleanupTestDevice(test_mac)
			cmd := fmt.Sprintf("config syslog set %s 1 ************* 514 6 1", "11-22-33-44-55-66")
			parts := strings.Split(cmd, " ")
			// Replace the appropriate part with malicious input
			switch tc.field {
			case "MAC":
				parts[3] = tc.input
			case "Status":
				parts[4] = tc.input
			case "IP":
				parts[5] = tc.input
			case "Port":
				parts[6] = tc.input
			}
			cmd = strings.Join(parts, " ")

			cmdinfo := &CmdInfo{Command: cmd}
			_, err := IsAValidConfigSyslogSetCmd(cmdinfo)
			assert.Error(t, err) // Should fail validation
		})
	}
}

// 5. Concurrency Tests
// Concurrent Command Processing
func TestConfigSyslogSetCmd_ConcurrentAccess(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)
	var wg sync.WaitGroup
	for i := 0; i < 100; i++ {
		wg.Add(1)
		go func(i int) {
			defer wg.Done()
			cmd := fmt.Sprintf("config syslog set 11-22-33-44-55-66 1 192.168.1.%d 514 6 1", i%10)
			cmdinfo := &CmdInfo{Command: cmd}
			_, err := IsAValidConfigSyslogSetCmd(cmdinfo)
			assert.NoError(t, err)
		}(i)
	}
	wg.Wait()
}

// ----------------------------------------Syslog Configuration Get---------------------------------------------------
// 1. Unit Test Cases
// Command Validation
func TestConfigSyslogGetCmd_ArgumentValidation(t *testing.T) {
	testCases := []struct {
		name     string
		command  string
		expected string
	}{
		{
			name:     "Valid command",
			command:  "config syslog get 11-22-33-44-55-66",
			expected: "",
		},
		{
			name:     "Missing MAC",
			command:  "config syslog get",
			expected: "error: invalid command arguments, expected 4 but got 3",
		},
		{
			name:     "Extra arguments",
			command:  "config syslog get 11-22-33-44-55-66 extra",
			expected: "error: invalid command arguments, expected 4 but got 5",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Inject device into mock store
			injectTestDevice(test_mac, map[string]bool{"agent": true})
			defer cleanupTestDevice(test_mac)
			cmdinfo := &CmdInfo{Command: tc.command}
			_, err := IsAValidConfigSyslogGetCmd(cmdinfo)

			if tc.expected == "" {
				assert.NoError(t, err)
			} else {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.expected)
			}
		})
	}
}

// MAC Address Validation
func TestConfigSyslogGetCmd_MACValidation(t *testing.T) {
	testCases := []struct {
		mac   string
		valid bool
	}{
		{"11-22-33-44-55-66", true},
		{"invalid-mac", false},
		{"", false},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("MAC_%s", tc.mac), func(t *testing.T) {
			// Inject device into mock store
			injectTestDevice(test_mac, map[string]bool{"agent": true})
			defer cleanupTestDevice(test_mac)
			cmd := fmt.Sprintf("config syslog get %s", tc.mac)
			cmdinfo := &CmdInfo{Command: cmd}
			_, err := IsAValidConfigSyslogGetCmd(cmdinfo)

			if tc.valid {
				assert.NoError(t, err)
			} else {
				assert.Error(t, err)
			}
		})
	}
}

// 2. Path Validation Tests
// Agent Path
func TestConfigSyslogGetCmd_AgentPath(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "config syslog get 11-22-33-44-55-66"}
	result, err := IsAValidConfigSyslogGetCmd(cmdinfo)

	assert.NoError(t, err)
	assert.Equal(t, "agent config syslog get 11-22-33-44-55-66", result.Command)
}

// SNMP Path
func TestConfigSyslogGetCmd_SNMPPath(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"gwd": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "config syslog get 11-22-33-44-55-66"}
	result, err := IsAValidConfigSyslogGetCmd(cmdinfo)

	assert.NoError(t, err)
	assert.Equal(t, "snmp config syslog get 11-22-33-44-55-66", result.Command)
}

// 3. Edge Case Tests
// Empty Command
func TestConfigSyslogGetCmd_EmptyCommand(t *testing.T) {
	cmdinfo := &CmdInfo{Command: ""}
	result, err := IsAValidConfigSyslogGetCmd(cmdinfo)

	assert.Error(t, err)
	assert.Contains(t, result.Status, "invalid command arguments")
}

// 4. Security Tests
// Command Injection
func TestConfigSyslogGetCmd_CommandInjection(t *testing.T) {
	testCases := []string{
		"config syslog get 11-22-33-44-55-66; rm -rf /",
		"config syslog get $(rm -rf /)",
	}

	for _, tc := range testCases {
		t.Run(tc, func(t *testing.T) {
			// Inject device into mock store
			injectTestDevice(test_mac, map[string]bool{"agent": true})
			defer cleanupTestDevice(test_mac)
			cmdinfo := &CmdInfo{Command: tc}
			_, err := IsAValidConfigSyslogGetCmd(cmdinfo)
			assert.Error(t, err)
		})
	}
}

// 5. Concurrency Tests
// Concurrent Access
func TestConfigSyslogGetCmd_ConcurrentAccess(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	var wg sync.WaitGroup
	for i := 0; i < 100; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			cmdinfo := &CmdInfo{Command: "config syslog get 11-22-33-44-55-66"}
			_, err := IsAValidConfigSyslogGetCmd(cmdinfo)
			assert.NoError(t, err)
		}()
	}
	wg.Wait()
}

// 6. Integration Test Cases
// Successful Agent Path
func TestConfigSyslogGetCmd_Integration_AgentSuccess(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "config syslog get 11-22-33-44-55-66"}
	result, err := IsAValidConfigSyslogGetCmd(cmdinfo)

	assert.NoError(t, err)
	assert.Equal(t, "agent config syslog get 11-22-33-44-55-66", result.Command)
}

// Successful SNMP Path
func TestConfigSyslogGetCmd_Integration_SNMPSuccess(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"gwd": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "config syslog get 11-22-33-44-55-66"}
	result, err := IsAValidConfigSyslogGetCmd(cmdinfo)

	assert.NoError(t, err)
	assert.Equal(t, "snmp config syslog get 11-22-33-44-55-66", result.Command)
}

// ----------------------------------------Firmware Upgrade---------------------------------------------------
// 1. Unit Test Cases
// Command Validation
func TestFWUpdateCmd_ArgumentValidation(t *testing.T) {
	testCases := []struct {
		name     string
		command  string
		expected string
	}{
		{
			name:     "Valid command",
			command:  "firmware update 11-22-33-44-55-66 firmware.bin",
			expected: "",
		},
		{
			name:     "Missing MAC and file",
			command:  "firmware update",
			expected: "expected 4 but got 2",
		},
		{
			name:     "Missing file",
			command:  "firmware update 11-22-33-44-55-66",
			expected: "expected 4 but got 3",
		},
		{
			name:     "Extra arguments",
			command:  "firmware update 11-22-33-44-55-66 firmware.bin extra",
			expected: "expected 4 but got 5",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Inject device into mock store
			injectTestDevice(test_mac, map[string]bool{"agent": true})
			defer cleanupTestDevice(test_mac)
			cmdinfo := &CmdInfo{Command: tc.command}
			_, err := IsAValidFWUpdateCmd(cmdinfo)

			if tc.expected == "" {
				assert.NoError(t, err)
			} else {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.expected)
			}
		})
	}
}

// MAC Address Validation
func TestFWUpdateCmd_MACValidation(t *testing.T) {
	testCases := []struct {
		mac   string
		valid bool
	}{
		{"11-22-33-44-55-66", true},
		{"invalid-mac", false},
		{"", false},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("MAC_%s", tc.mac), func(t *testing.T) {
			// Inject device into mock store
			injectTestDevice(test_mac, map[string]bool{"agent": true})
			defer cleanupTestDevice(test_mac)
			cmd := fmt.Sprintf("firmware update %s firmware.bin", tc.mac)
			cmdinfo := &CmdInfo{Command: cmd}
			_, err := IsAValidFWUpdateCmd(cmdinfo)

			if tc.valid {
				assert.NoError(t, err)
			} else {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "invalid MAC address")
			}
		})
	}
}

// Device Lookup
func TestFWUpdateCmd_DeviceLookup(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "firmware update 11-22-33-44-55-77 firmware.bin"}
	result, err := IsAValidFWUpdateCmd(cmdinfo)

	assert.Error(t, err)
	assert.Contains(t, result.Status, "device not found")
}

// 2. Path Validation Tests
// Agent Path
func TestFWUpdateCmd_AgentPath(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "firmware update 11-22-33-44-55-66 firmware.bin"}
	result, err := IsAValidFWUpdateCmd(cmdinfo)

	assert.NoError(t, err)
	assert.Equal(t, "agent firmware update 11-22-33-44-55-66 firmware.bin", result.Command)
}

// GWD Path
func TestFWUpdateCmd_GWDPath(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"gwd": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "firmware update 11-22-33-44-55-66 firmware.bin"}
	result, err := IsAValidFWUpdateCmd(cmdinfo)

	assert.NoError(t, err)
	assert.Equal(t, "gwd firmware update 11-22-33-44-55-66 firmware.bin", result.Command)
}

// EHG2408 Special Case
func TestFWUpdateCmd_EHG2408RequiresAgent(t *testing.T) {
	// Setup test device in QC.DevData
	testDev := &DevInfo{
		Mac:       "11-22-33-44-55-66",
		ModelName: "EHG2408",
		Capabilities: map[string]bool{
			"gwd": true,
		},
	}
	QC.DevMutex.Lock()
	QC.DevData[testDev.Mac] = *testDev
	QC.DevMutex.Unlock()

	defer func() {
		QC.DevMutex.Lock()
		delete(QC.DevData, testDev.Mac)
		QC.DevMutex.Unlock()
	}()

	cmdinfo := &CmdInfo{Command: "firmware update 11-22-33-44-55-66 firmware.bin"}
	result, err := IsAValidFWUpdateCmd(cmdinfo)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "must required agent")
	assert.Contains(t, result.Status, "must required agent")
}

// 3. Edge Case Tests
// File Name Validation
func TestFWUpdateCmd_FileNameValidation(t *testing.T) { //TODO:Need to verify the file name validation
	testCases := []struct {
		filename string
		valid    bool
		errMsg   string // Add expected error message
	}{
		{"firmware.bin", true, ""},
		{"fw_v1.2.3.bin", true, ""},
		//{"firmware_v1.2.3.tar.gz", false, "invalid file extension"},
		//{"invalid_name.bin", false, "invalid characters in filename"},
		//{"../../../etc/passwd", false, "path traversal not allowed"},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("File_%s", tc.filename), func(t *testing.T) {
			// Inject device into mock store
			injectTestDevice(test_mac, map[string]bool{"agent": true})
			defer cleanupTestDevice(test_mac)

			cmd := fmt.Sprintf("firmware update 11-22-33-44-55-66 %s", tc.filename)
			cmdinfo := &CmdInfo{Command: cmd}
			_, err := IsAValidFWUpdateCmd(cmdinfo)

			if tc.valid {
				assert.NoError(t, err)
			} else {
				assert.Error(t, err)
			}
		})
	}
}

// 4. Security Tests
// Command Injection
func TestFWUpdateCmd_CommandInjection(t *testing.T) {
	testCases := []struct {
		input string
		field string
	}{
		{"11-22-33-44-55-66; rm -rf /", "MAC"},
		{"firmware.bin; shutdown", "filename"},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("Injection_%s", tc.field), func(t *testing.T) {
			cmd := "firmware update 11-22-33-44-55-66 firmware.bin"
			parts := strings.Split(cmd, " ")
			if tc.field == "Mac" {
				parts[2] = tc.input
			} else {
				parts[3] = tc.input
			}
			cmd = strings.Join(parts, " ")

			cmdinfo := &CmdInfo{Command: cmd}
			_, err := IsAValidFWUpdateCmd(cmdinfo)
			assert.Error(t, err) // Should fail validation
		})
	}
}

// 5. Integration Test Cases
// Successful Agent Path
func TestFWUpdateCmd_Integration_AgentSuccess(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "firmware update 11-22-33-44-55-66 firmware.bin"}
	result, err := IsAValidFWUpdateCmd(cmdinfo)

	assert.NoError(t, err)
	assert.Equal(t, "agent firmware update 11-22-33-44-55-66 firmware.bin", result.Command)
}

// Successful GWD Path
func TestFWUpdateCmd_Integration_GWDSuccess(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"gwd": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "firmware update 11-22-33-44-55-66 firmware.bin"}
	result, err := IsAValidFWUpdateCmd(cmdinfo)

	assert.NoError(t, err)
	assert.Equal(t, "gwd firmware update 11-22-33-44-55-66 firmware.bin", result.Command)
}

// 6. Concurrency Tests
// Concurrent Access
func TestFWUpdateCmd_ConcurrentAccess(t *testing.T) {
	// Setup test device
	testDev := &DevInfo{
		Mac:          "11-22-33-44-55-66",
		Capabilities: map[string]bool{"agent": true},
	}

	QC.DevMutex.Lock()
	QC.DevData[testDev.Mac] = *testDev
	QC.DevMutex.Unlock()
	defer func() {
		QC.DevMutex.Lock()
		delete(QC.DevData, testDev.Mac)
		QC.DevMutex.Unlock()
	}()

	var wg sync.WaitGroup
	for i := 0; i < 100; i++ {
		wg.Add(1)
		go func(i int) {
			defer wg.Done()
			cmd := fmt.Sprintf("firmware update 11-22-33-44-55-66 firmware%d.bin", i)
			cmdinfo := &CmdInfo{Command: cmd}
			_, err := IsAValidFWUpdateCmd(cmdinfo)
			assert.NoError(t, err)
		}(i)
	}
	wg.Wait()
}

// ----------------------------------------Switch Configuration commands ---------------------------------------------------
// 1. Unit Test Cases
// Command Validation
func TestSwitchCmd_ArgumentValidation(t *testing.T) { // Need switch device todo testcase pass
	// Create package-level variables for mocking

	testCases := []struct {
		name     string
		command  string
		expected string
	}{
		{
			name:     "Missing arguments",
			command:  "switch",
			expected: "error: invalid command",
		},
		{
			name:     "Invalid config save format",
			command:  "switch config save",
			expected: "error: invalid command",
		},
		{
			name:     "Extra arguments in config save",
			command:  "switch config save 11-22-33-44-55-66 extra",
			expected: "error: too many arguments",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Setup test device
			testDev := &DevInfo{
				Mac:       "11-22-33-44-55-66",
				ModelName: "EHG7508",
				Capabilities: map[string]bool{
					"gwd": true,
				},
			}
			QC.DevMutex.Lock()
			QC.DevData[testDev.Mac] = *testDev
			QC.DevMutex.Unlock()

			defer func() {
				QC.DevMutex.Lock()
				delete(QC.DevData, testDev.Mac)
				QC.DevMutex.Unlock()
			}()

			originalCheckModel = func(model string) bool { return true }
			originalSendSwitch = func(cmdinfo *CmdInfo, dev *DevInfo, user, pass, cmd string, variant int) error {
				return nil
			}

			// Ensure we restore original functions
			defer func() {
				findDevice = FindDev
				originalCheckModel = CheckSwitchCliModel
				originalSendSwitch = SendSwitch
			}()

			cmdinfo := &CmdInfo{Command: tc.command}
			result := SwitchCmd(cmdinfo)

			if tc.expected == "ok" {
				assert.Equal(t, "ok", result.Status)
			} else {
				assert.Contains(t, result.Status, tc.expected)
			}
		})
	}
}

// 1. MAC Address Validation Test
func TestSwitchCmd_MACValidation(t *testing.T) {
	testCases := []struct {
		mac   string
		valid bool
	}{
		{"11-22-33-44-55-66", true},
		{"invalid-mac", false},
		{"", false},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("MAC_%s", tc.mac), func(t *testing.T) {
			// Inject device into mock store
			injectTestDevice(test_mac, map[string]bool{"agent": true})
			defer cleanupTestDevice(test_mac)

			cmd := fmt.Sprintf("switch %s show ip", tc.mac)
			cmdinfo := &CmdInfo{Command: cmd}
			result := SwitchCmd(cmdinfo)

			if tc.valid {
				assert.NotContains(t, result.Status, "invalid MAC Address")
			} else {
				assert.Contains(t, result.Status, "invalid MAC Address")
			}
		})
	}
}

// Device Lookup Test
func TestSwitchCmd_DeviceLookup(t *testing.T) {
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"gwd": true})
	defer cleanupTestDevice(test_mac)

	cmdinfo := &CmdInfo{Command: "switch 11-22-33-44-55-77 show ip"}
	result := SwitchCmd(cmdinfo)

	assert.Contains(t, result.Status, "device not found")
}

// EHG2408 Special Cases Test
func TestSwitchCmd_EHG2408Restrictions(t *testing.T) {
	testCases := []struct {
		command  string
		expected string
	}{
		{"switch 11-22-33-44-55-66 snmp enable", "must required agent to run the snmp enable command"},
		{"switch 11-22-33-44-55-66 no snmp", "must required agent to run the snmp disable command"},
		{"switch 11-22-33-44-55-66 snmp trap", "must required agent to run the trap setting command"},
		{"switch 11-22-33-44-55-66 show snmp trap", "must required agent to run the get trap settings command"},
	}
	// Setup test device in QC.DevData
	// Setup test device
	testDev := &DevInfo{
		Mac:       "11-22-33-44-55-66",
		ModelName: "EHG2408",
		Capabilities: map[string]bool{
			"gwd": true,
		},
	}
	QC.DevMutex.Lock()
	QC.DevData[testDev.Mac] = *testDev
	QC.DevMutex.Unlock()

	defer func() {
		QC.DevMutex.Lock()
		delete(QC.DevData, testDev.Mac)
		QC.DevMutex.Unlock()
	}()

	for _, tc := range testCases {
		t.Run(tc.command, func(t *testing.T) {
			cmdinfo := &CmdInfo{Command: tc.command}
			result := SwitchCmd(cmdinfo)
			assert.Contains(t, result.Status, tc.expected)
		})
	}
}


// Model Compatibility Test
func TestSwitchCmd_ModelCompatibility(t *testing.T) { // // Need switch device todo testcase pass
	testCases := []struct {
		model      string
		compatible bool
	}{
		{"EHG3200", true},
		{"EHG75", true},
		{"EHG76", true},
		{"EMG", true},
		{"RHG", true},
		{"UnknownModel", false},
	}

	for _, tc := range testCases {
		t.Run(tc.model, func(t *testing.T) {
				// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)
			findDevice = func(id string) (*DevInfo, error) {
				return &DevInfo{
					Mac:       "11-22-33-44-55-66",
					ModelName: tc.model,
				}, nil
			}
			defer func() { findDevice = FindDev }()

			originalCheckModel = func(model string) bool { return tc.compatible }
			defer func() { originalCheckModel = CheckSwitchCliModel }()

			cmdinfo := &CmdInfo{Command: "switch 11-22-33-44-55-66 show ip"}
			result := SwitchCmd(cmdinfo)

			if tc.compatible {
				assert.Equal(t, "ok", result.Status)
			} else {
				assert.Contains(t, result.Status, "switch cli not available")
			}
		})
	}
}
// Config Save Command
func TestSwitchCmd_ConfigSave(t *testing.T) { // // Need switch device todo testcase pass
	testCases := []struct {
		model   string
		variant int
	}{
		{"EHG75", 2},
		{"EHG76", 2},
		{"EMG", 2},
		{"RHG", 2},
		{"EHG65", 3},
		{"EHG24", 3},
		{"EHG3200", 1},
	}

	for _, tc := range testCases {
		t.Run(tc.model, func(t *testing.T) {
			var actualVariant int
			var actualCmd string

			// Setup mocks
				// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)
			originalSendSwitch = func(cmdinfo *CmdInfo, dev *DevInfo, user, pass, cmd string, variant int) error {
				actualCmd = cmd
				actualVariant = variant
				return nil
			}
			defer func() { originalSendSwitch = SendSwitch }()

			cmdinfo := &CmdInfo{Command: "switch config save 11-22-33-44-55-66"}
			result := SwitchCmd(cmdinfo)

			assert.Equal(t, "ok", result.Status)
			assert.Equal(t, "copy running-config startup-config", actualCmd)
			assert.Equal(t, tc.variant, actualVariant)
		})
	}
}

// Command Conversion Test
func TestSwitchCmd_CommandConversion(t *testing.T) { // Need switch device todo testcase pass
	// Save original implementations
	//originalSendSwitch := SendSwitch
	originalConvertSwitchCmd := convertSwitchCmd
	defer func() {
		originalSendSwitch = SendSwitch
		convertSwitchCmd = originalConvertSwitchCmd
	}()

	testCases := []struct {
		input       string
		expectError bool
		errorMsg    string
	}{
		{
			input:       "show ip",
			expectError: true, // Since we know it will try to connect
			errorMsg:    "dial tcp :23",
		},
		{
			input:       "configure terminal",
			expectError: true,
			errorMsg:    "dial tcp :23",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.input, func(t *testing.T) {
			// Setup test device
			testDev := &DevInfo{
				Mac:       "11-22-33-44-55-66",
				ModelName: "EHG7508",
				Capabilities: map[string]bool{
					"gwd": true,
				},
			}
			QC.DevMutex.Lock()
			QC.DevData[testDev.Mac] = *testDev
			QC.DevMutex.Unlock()

			defer func() {
				QC.DevMutex.Lock()
				delete(QC.DevData, testDev.Mac)
				QC.DevMutex.Unlock()
			}()

			cmd := fmt.Sprintf("switch 11-22-33-44-55-66 %s", tc.input)
			cmdinfo := &CmdInfo{Command: cmd}
			result := SwitchCmd(cmdinfo)

			if tc.expectError {
				assert.Contains(t, result.Status, tc.errorMsg)
			} else {
				assert.Equal(t, "ok", result.Status)
			}
		})
	}
}

// Error Handling Tests
// SendSwitch Errors
func TestSwitchCmd_SendSwitchErrors(t *testing.T) { // Need switch case device todo
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"gwd": true})
	defer cleanupTestDevice(test_mac)
	originalSendSwitch = func(cmdinfo *CmdInfo, dev *DevInfo, user, pass, cmd string, variant int) error {
		return fmt.Errorf("switch cli not available")
	}
	defer func() { originalSendSwitch = SendSwitch }()

	cmdinfo := &CmdInfo{Command: "switch 11-22-33-44-55-66 show ip"}
	result := SwitchCmd(cmdinfo)

	assert.Contains(t, result.Status, "switch cli not available")
}

// ConvertSwitchCmd Errors
func TestSwitchCmd_ConvertErrors(t *testing.T) { // Need switch device todo testcase pass
	// Inject device into mock store
	injectTestDevice(test_mac, map[string]bool{"agent": true})
	defer cleanupTestDevice(test_mac)

	originalConvertSwitchCmd := convertSwitchCmd
	convertSwitchCmd = func(model string, cmd []string) (_ []string, S error) {
		return nil, fmt.Errorf("invalid command")
	}
	defer func() { convertSwitchCmd = originalConvertSwitchCmd }()

	cmdinfo := &CmdInfo{Command: "switch 11-22-33-44-55-66 invalid-command"}
	result := SwitchCmd(cmdinfo)

	assert.Contains(t, result.Status, "invalid command")
}

// ----------------------------------------Snmp Configuration commands ---------------------------------------------------

// Test setup with mock data
func setupTest() {
	QC.DevData = make(map[string]DevInfo)
	QC.DevData["11-22-33-44-55-66"] = DevInfo{
		Mac:          "11-22-33-44-55-66",
		Capabilities: map[string]bool{"agent": true}, // Agent-capable device
		Lock:         false,
	}
}

func teardownTest() {
	QC.DevData = nil
}

// TestValidateSnmpEnableDisable tests SNMP enable/disable commands
func TestValidateSnmpEnableDisable(t *testing.T) {
	setupTest()
	defer teardownTest()

	tests := []struct {
		name    string
		command string
		wantCmd string // Expected command after validation
	}{
		{
			name:    "Enable SNMP",
			command: "snmp enable 11-22-33-44-55-66",
			wantCmd: "agent snmp enable 11-22-33-44-55-66 1",
		},
		{
			name:    "Disable SNMP",
			command: "snmp disable 11-22-33-44-55-66",
			wantCmd: "agent snmp enable 11-22-33-44-55-66 0",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmd := &CmdInfo{
				Command:   tt.command,
				Timestamp: time.Now().Format(time.RFC3339),
			}

			result, err := ValidateSnmpCmd(cmd)
			if err != nil {
				t.Errorf("ValidateSnmpCmd() error = %v", err)
				return
			}

			if result.Command != tt.wantCmd {
				t.Errorf("ValidateSnmpCmd() got = %v, want %v", result.Command, tt.wantCmd)
			}
		})
	}
}

// TestValidateSnmpTrapCommands tests SNMP trap commands
func TestValidateSnmpTrapCommands(t *testing.T) {
	setupTest()
	defer teardownTest()

	tests := []struct {
		name    string
		command string
		wantCmd string
	}{
		{
			name:    "Add Trap Server",
			command: "snmp trap add 11-22-33-44-55-66 ************* 162 public",
			wantCmd: "agent snmp trap add 11-22-33-44-55-66 ************* 162 public",
		},
		{
			name:    "Delete Trap Server",
			command: "snmp trap del 11-22-33-44-55-66 ************* 162 public",
			wantCmd: "agent snmp trap del 11-22-33-44-55-66 ************* 162 public",
		},
		{
			name:    "Get Trap Servers",
			command: "snmp trap get 11-22-33-44-55-66",
			wantCmd: "agent snmp trap get 11-22-33-44-55-66",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmd := &CmdInfo{
				Command:   tt.command,
				Timestamp: time.Now().Format(time.RFC3339),
			}

			result, err := ValidateSnmpCmd(cmd)
			if err != nil {
				t.Errorf("ValidateSnmpCmd() error = %v", err)
				return
			}

			if result.Command != tt.wantCmd {
				t.Errorf("ValidateSnmpCmd() got = %v, want %v", result.Command, tt.wantCmd)
			}
		})
	}
}

// TestValidateSnmpSyslogCommands tests SNMP syslog commands
func TestValidateSnmpSyslogCommands(t *testing.T) {
	setupTest()
	defer teardownTest()

	tests := []struct {
		name    string
		command string
	}{
		{
			name:    "Set Syslog Configuration",
			command: "snmp config syslog set 11-22-33-44-55-66 1 ******** 514 6 1",
		},
		{
			name:    "Get Syslog Configuration",
			command: "snmp config syslog get 11-22-33-44-55-66",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmd := &CmdInfo{
				Command:   tt.command,
				Timestamp: time.Now().Format(time.RFC3339),
			}

			_, err := ValidateSnmpCmd(cmd)
			if err != nil {
				t.Errorf("ValidateSnmpCmd() error = %v", err)
			}
		})
	}
}

// TestValidateSnmpOptions tests SNMP options command
func TestValidateSnmpOptions(t *testing.T) {
	tests := []struct {
		name    string
		command string
	}{
		{
			name:    "Set SNMP Options",
			command: "snmp options 161 3 2c 30",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmd := &CmdInfo{
				Command:   tt.command,
				Timestamp: time.Now().Format(time.RFC3339),
			}

			_, err := ValidateSnmpCmd(cmd)
			if err != nil {
				t.Errorf("ValidateSnmpCmd() error = %v", err)
			}
		})
	}
}

// TestValidateSnmpCommunityCommands tests SNMP community commands
func TestValidateSnmpCommunityCommands(t *testing.T) {
	setupTest()
	defer teardownTest()

	tests := []struct {
		name    string
		command string
	}{
		{
			name:    "Get Communities",
			command: "snmp communities 11-22-33-44-55-66",
		},
		{
			name:    "Update Communities",
			command: "snmp update community 11-22-33-44-55-66 public private",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmd := &CmdInfo{
				Command:   tt.command,
				Timestamp: time.Now().Format(time.RFC3339),
			}

			_, err := ValidateSnmpCmd(cmd)
			if err != nil {
				t.Errorf("ValidateSnmpCmd() error = %v", err)
			}
		})
	}
}

// TestValidateLegacySnmpCommand tests legacy SNMP command
func TestValidateLegacySnmpCommand(t *testing.T) {
	tests := []struct {
		name    string
		command string
	}{
		{
			name:    "Legacy SNMP Command",
			command: "snmp ***********",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmd := &CmdInfo{
				Command:   tt.command,
				Timestamp: time.Now().Format(time.RFC3339),
			}

			_, err := ValidateSnmpCmd(cmd)
			if err != nil {
				t.Errorf("ValidateSnmpCmd() error = %v", err)
			}
		})
	}
}

// TestValidateSnmpNonAgentDevice tests commands for non-agent devices
func TestValidateSnmpNonAgentDevice(t *testing.T) {
	setupTest()
	// Override with non-agent device
	QC.DevData["11-22-33-44-55-66"] = DevInfo{
		Mac:          "11-22-33-44-55-66",
		Capabilities: map[string]bool{"gwd": true},
		Lock:         false,
	}
	defer teardownTest()

	tests := []struct {
		name    string
		command string
		wantCmd string
	}{
		{
			name:    "Enable SNMP on non-agent device",
			command: "snmp enable 11-22-33-44-55-66",
			wantCmd: "switch 11-22-33-44-55-66 snmp enable",
		},
		{
			name:    "Disable SNMP on non-agent device",
			command: "snmp disable 11-22-33-44-55-66",
			wantCmd: "switch 11-22-33-44-55-66 no snmp enable",
		},
		{
			name:    "Trap command on non-agent device",
			command: "snmp trap add 11-22-33-44-55-66 ************* 162 public",
			wantCmd: "switch 11-22-33-44-55-66 snmp trap ************* public 162",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmd := &CmdInfo{
				Command:   tt.command,
				Timestamp: time.Now().Format(time.RFC3339),
			}

			result, err := ValidateSnmpCmd(cmd)
			if err != nil {
				t.Errorf("ValidateSnmpCmd() error = %v", err)
				return
			}

			if result.Command != tt.wantCmd {
				t.Errorf("ValidateSnmpCmd() got = %v, want %v", result.Command, tt.wantCmd)
			}
		})
	}
}

// TestValidateSnmpEdgeCases tests edge cases with valid but extreme inputs
func TestValidateSnmpEdgeCases(t *testing.T) {
	setupTest()
	defer teardownTest()

	tests := []struct {
		name    string
		command string
	}{
		{
			name:    "Max port number",
			command: "snmp trap add 11-22-33-44-55-66 *********** 65535 public",
		},
		{
			name:    "Min port number",
			command: "snmp trap add 11-22-33-44-55-66 *********** 1 public",
		},
		{
			name:    "Max log level",
			command: "snmp config syslog set 11-22-33-44-55-66 1 ******** 514 7 1",
		},
		{
			name:    "Min log level",
			command: "snmp config syslog set 11-22-33-44-55-66 1 ******** 514 0 1",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmd := &CmdInfo{
				Command:   tt.command,
				Timestamp: time.Now().Format(time.RFC3339),
			}

			_, err := ValidateSnmpCmd(cmd)
			if err != nil {
				t.Errorf("ValidateSnmpCmd() error = %v", err)
			}
		})
	}
}

// 1 Command Parsing & Validation
func TestCommandParsing(t *testing.T) {
	tests := []struct {
		name    string
		command string
		wantErr bool
	}{
		{"Empty Command", "", true},
		{"Invalid Command", "invalid command", true},
		{"Short Command", "snmp", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := ValidateSnmpCmd(&CmdInfo{Command: tt.command})
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateSnmpCmd() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// Device Agent Capability Handling
func TestDeviceCapabilities_Agent(t *testing.T) {
	setupTest()
	defer teardownTest()

	// Test agent vs non-agent devices
	tests := []struct {
		name    string
		command string
		wantCmd string
	}{
		{
			"Agent Device - Enable SNMP",
			"snmp enable 11-22-33-44-55-66",
			"agent snmp enable 11-22-33-44-55-66 1",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Inject device into mock store
			injectTestDevice(test_mac, map[string]bool{"agent": true})
			defer cleanupTestDevice(test_mac)

			cmd := &CmdInfo{Command: tt.command}
			result, err := ValidateSnmpCmd(cmd)
			if err != nil {
				t.Fatalf("ValidateSnmpCmd() error = %v", err)
			}
			if result.Command != tt.wantCmd {
				t.Errorf("got = %v, want %v", result.Command, tt.wantCmd)
			}
		})
	}
}

// Device Capability Handling
func TestDeviceCapabilities_GWD(t *testing.T) {
	setupTest()
	defer teardownTest()

	// Test agent vs non-agent devices
	tests := []struct {
		name    string
		command string
		wantCmd string
	}{
		{
			"Non-Agent Device - Enable SNMP",
			"snmp enable 11-22-33-44-55-66",
			"switch 11-22-33-44-55-66 snmp enable",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Inject device into mock store
			injectTestDevice(test_mac, map[string]bool{"gwd": true})
			defer cleanupTestDevice(test_mac)

			cmd := &CmdInfo{Command: tt.command}
			result, err := ValidateSnmpCmd(cmd)
			if err != nil {
				t.Fatalf("ValidateSnmpCmd() error = %v", err)
			}
			if result.Command != tt.wantCmd {
				t.Errorf("got = %v, want %v", result.Command, tt.wantCmd)
			}
		})
	}
}

// Device Locking Scenarios
func TestDeviceLocking(t *testing.T) {
	setupTest()
	defer teardownTest()

	// Lock the device
	dev := QC.DevData["11-22-33-44-55-66"]
	dev.Lock = true
	QC.DevData["11-22-33-44-55-66"] = dev

	cmd := &CmdInfo{Command: "snmp trap add 11-22-33-44-55-66 *********** 162 public"}
	_, err := ValidateSnmpCmd(cmd)
	if err == nil {
		t.Error("Expected error for locked device, got nil")
	}
}

// Invalid IP/Port Ranges
func TestInvalidNetworkParams(t *testing.T) {
	tests := []struct {
		name    string
		command string
		wantErr bool
	}{
		{"Invalid IP", "snmp trap add 11-22-33-44-55-66 256.168.1.1 162 public", true},
		{"Invalid Port (0)", "snmp trap add 11-22-33-44-55-66 *********** 0 public", true},
		{"Invalid Port (65536)", "snmp trap add 11-22-33-44-55-66 *********** 65536 public", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := ValidateSnmpCmd(&CmdInfo{Command: tt.command})
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateSnmpCmd() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// Missing Required Fields
func TestMissingFields(t *testing.T) {
	tests := []struct {
		name    string
		command string
		wantErr bool
	}{
		{"Missing MAC", "snmp enable", true},
		{"Missing IP", "snmp trap add 11-22-33-44-55-66", true},
		{"Missing Community", "snmp trap add 11-22-33-44-55-66 *********** 162", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := ValidateSnmpCmd(&CmdInfo{Command: tt.command})
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateSnmpCmd() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// Unknown Subcommands
func TestUnknownSubcommand(t *testing.T) {
	cmd := &CmdInfo{Command: "snmp invalid 11-22-33-44-55-66"}
	_, err := ValidateSnmpCmd(cmd)
	if err == nil {
		t.Error("Expected error for unknown subcommand, got nil")
	}
}

// Full Integration Test (End-to-End)
func TestEndToEndSNMPFlow(t *testing.T) {
	setupTest()
	defer teardownTest()

	// Step 1: Enable SNMP
	enableCmd := &CmdInfo{Command: "snmp enable 11-22-33-44-55-66"}
	_, err := ValidateSnmpCmd(enableCmd)
	if err != nil {
		t.Fatalf("Enable SNMP failed: %v", err)
	}

	// Step 2: Add Trap Server
	trapCmd := &CmdInfo{Command: "snmp trap add 11-22-33-44-55-66 ************* 162 public"}
	_, err = ValidateSnmpCmd(trapCmd)
	if err != nil {
		t.Fatalf("Add Trap Server failed: %v", err)
	}

	// Step 3: Verify configuration
	getCmd := &CmdInfo{Command: "snmp trap get 11-22-33-44-55-66"}
	_, err = ValidateSnmpCmd(getCmd)
	if err != nil {
		t.Fatalf("Get Trap Servers failed: %v", err)
	}
}

func setupRealEnvironment(model string) {
	// Initialize with real device data
	QC = QContext{
		DevData: map[string]DevInfo{
			"11-22-33-44-55-66": {
				Mac:       "11-22-33-44-55-66",
				IPAddress: "*********00", // Use a test IP that won't conflict
				Lock:      false,
				Netmask:   "*************",
				Gateway:   "*********",
				Hostname:  "switch",
				UserName:  "admin",
				PassWord:  "default",
				ModelName: model,
			},
		},
	}
}

// ----------------------------------------Gwd beep Command---------------------------------------------------

// Device Not Found
func TestGwdBeepCmd_DeviceNotFound(t *testing.T) {
	model := "EHG75xx"
	setupRealEnvironment(model)
	cmdinfo := &CmdInfo{
		Command: "gwd beep 11-22-33-44-55-77",
		DevId:   "11-22-33-44-55-77",
	}
	result, _ := IsAValidGwdBeepCmd(cmdinfo)
	if result.Status != "error: device not found" {
		t.Errorf("Expected 'error: device not found', got '%s'", result.Status)
	}
}

// Unsupported Device Model (EHG2408)
func TestGwdBeepCmd_UnsupportedModelEHG2408(t *testing.T) {
	model := "EHG2408"
	setupRealEnvironment(model)
	cmdinfo := &CmdInfo{
		Command: "gwd beep 11-22-33-44-55-66",
		DevId:   "11-22-33-44-55-66",
	}

	result, _ := IsAValidGwdBeepCmd(cmdinfo)
	expectedStatus := "error: EHG2408 device does not support beep"
	if result.Status != expectedStatus {
		t.Errorf("Expected '%s', got '%s'", expectedStatus, result.Status)
	}
}

// // Unsupported Device Model (EHG65)
func TestGwdBeepCmd_UnsupportedModelEHG65(t *testing.T) {
	model := "EHG65"
	setupRealEnvironment(model)
	cmdinfo := &CmdInfo{
		Command: "gwd beep 11-22-33-44-55-66",
		DevId:   "11-22-33-44-55-66",
	}

	result, _ := IsAValidGwdBeepCmd(cmdinfo)
	expectedStatus := "error: EHG65 device does not support beep"
	if result.Status != expectedStatus {
		t.Errorf("Expected '%s', got '%s'", expectedStatus, result.Status)
	}
}

// Valid Command with Different MAC Address
func TestGwdBeepCmd_ValidCommandDifferentMAC(t *testing.T) {
	model := "EHG75xx"
	setupRealEnvironment(model)
	cmdinfo := &CmdInfo{
		Command: "gwd beep AA-BB-CC-DD-EE-FF",
		DevId:   "AA-BB-CC-DD-EE-FF",
	}
	result, _ := IsAValidGwdBeepCmd(cmdinfo)
	if result.Status != "error: device not found" {
		t.Errorf("Expected status 'error: device not found', got '%s'", result.Status)
	}
}

// Invalid Command Format (Too Few Arguments)
func TestGwdBeepCmd_InvalidCommandFormat(t *testing.T) {
	model := "EHG75xx"
	setupRealEnvironment(model)
	cmdinfo := &CmdInfo{
		Command: "gwd beep",
		DevId:   "11-22-33-44-55-66",
	}

	result, _ := IsAValidGwdBeepCmd(cmdinfo)
	if result.Status != "error: invalid command arguments, expected 3 but got 2" {
		t.Errorf("Expected '%s', got '%s'", "error: invalid command arguments, expected 3 but got 2", result.Status)
	}
}

// Invalid MAC Address Format
func TestGwdBeepCmd_InvalidMACFormat(t *testing.T) {
	model := "EHG75xx"
	setupRealEnvironment(model)
	cmdinfo := &CmdInfo{
		Command: "gwd beep 11-22-33-44-55-6G",
		DevId:   "11-22-33-44-55-6G",
	}

	result, _ := IsAValidGwdBeepCmd(cmdinfo)
	if result.Status != "error: invalid MAC Address" {
		t.Errorf("Expected '%s', got '%s'", "error: invalid MAC Address", result.Status)
	}
}

// --------------------------------GwdReset Command---------------------------------------
// Device Not Found
func TestGwdResetCmd_DeviceNotFound(t *testing.T) {
	model := "EHG75xx"
	setupRealEnvironment(model)
	cmdinfo := &CmdInfo{
		Command: "gwd reset 11-22-33-44-55-77",
		DevId:   "11-22-33-44-55-77",
	}
	result, _ := IsAValidGwdResetCmd(cmdinfo)
	if result.Status != "error: device not found" {
		t.Errorf("Expected 'error: device not found', got '%s'", result.Status)
	}
}

// Successful Reset with Different MAC
func TestGwdResetCmd_DifferentValidMAC(t *testing.T) {
	model := "EHG75xx"
	setupRealEnvironment(model)
	cmdinfo := &CmdInfo{
		Command: "gwd reset AA-BB-CC-DD-EE-FF",
		DevId:   "AA-BB-CC-DD-EE-FF",
	}
	result, _ := IsAValidGwdResetCmd(cmdinfo)
	if result.Status != "error: device not found" {
		t.Errorf("Expected status 'error: device not found', got '%s'", result.Status)
	}
}

// -------------------------------------GwdMtdEraseCmd ----------------------------

func TestGwdMtdEraseCmd_DeviceNotFound(t *testing.T) {
	model := "EHG75xx"
	setupRealEnvironment(model)
	cmd := &CmdInfo{
		Command: "gwd mtderase AA-BB-CC-DD-EE-FF",
		DevId:   "AA-BB-CC-DD-EE-FF", // Non-existent MAC
	}
	result, _ := IsAValidGwdMtdEraseCmd(cmd)
	if result.Status != "error: device not found" {
		t.Errorf("Expected device not found error, got '%s'", result.Status)
	}
}

func TestGwdMtdEraseCmd_InvalidMACFormat(t *testing.T) {
	model := "EHG75xx"
	setupRealEnvironment(model)
	cmd := &CmdInfo{
		DevId:   "11-22-33-44-55-GG",
		Command: "gwd mtderase 11-22-33-44-55-GG",
	}

	result, _ := IsAValidGwdMtdEraseCmd(cmd)
	if result.Status != "error: invalid MAC Address" {
		t.Errorf("Expected status error: device not found ', got '%s'", result.Status)
	}
}

// -------------------------------------GwdConfigNetwork Cmd ----------------------------
  
func TestGwdConfigNetworkCmd_InvalidInputs(t *testing.T) {
	model := "EHG75xx"
	setupRealEnvironment(model)
  
	tests := []struct {
		name           string
		command        string
		expectedError  string
		expectedStatus string
	}{
		{
			name:           "Too few arguments",
			command:        "gwd config network set 11-22-33-44-55-66",
			expectedError:  "error: invalid command arguments, expected 10 but got 5",
			expectedStatus: "error: invalid command arguments, expected 10 but got 5",
		},
		{
			name:           "Invalid current IP",
			command:        "gwd config network set 11-22-33-44-55-66 300.0.50.1 ********* ************* ********* switch",
			expectedError:  "IP Address: 300.0.50.1 - Invalid",
			expectedStatus: "error: IP Address: 300.0.50.1 - Invalid",
		},
		{
			name:           "Invalid new IP",
			command:        "gwd config network set 11-22-33-44-55-66 ********* 300.0.50.2 ************* ********* switch",
			expectedError:  "IP Address: 300.0.50.2 - Invalid",
			expectedStatus: "error: IP Address: 300.0.50.2 - Invalid",
		},
		{
			name:           "Invalid netmask",
			command:        "gwd config network set 11-22-33-44-55-66 ********* ********* 255.255.300.0 ********* switch",
			expectedError:  "IP Address: 255.255.300.0 - Invalid",
			expectedStatus: "error: IP Address: 255.255.300.0 - Invalid",
		},
		{
			name:           "Invalid gateway",
			command:        "gwd config network set 11-22-33-44-55-66 ********* ********* ************* 300.0.50.1 switch",
			expectedError:  "IP Address: 300.0.50.1 - Invalid",
			expectedStatus: "error: IP Address: 300.0.50.1 - Invalid",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmd := &CmdInfo{
				Command: tt.command,
				DevId:   "11-22-33-44-55-66",
			}
			_, err := IsAValidGwdConfigCmd(cmd)

			// Check error
			if tt.expectedError != "" {
				if err == nil {
					t.Fatalf("Expected error '%s', got nil", tt.expectedError)
				}
				if !strings.Contains(err.Error(), tt.expectedError) {
					t.Fatalf("Expected error containing '%s', got '%v'", tt.expectedError, err)
				}
			} else if err != nil {
				t.Fatalf("Expected no error, got: %v", err)
			}

			// Check status
			if !strings.Contains(cmd.Status, tt.expectedStatus) {
				t.Errorf("Expected status containing '%s', got '%s'", tt.expectedStatus, cmd.Status)
			}
		})
	}
}

// -------------------------------------GwdFirmware update Cmd ----------------------------
func TestGwdFirmwareCmd_RealErrorCases(t *testing.T) {
	model := "EHG75xx"
	setupRealEnvironment(model)

	tests := []struct {
		name           string
		command        string
		devId          string
		prepare        func()
		expectedError  string
		expectedStatus string
	}{
		{
			name:           "Device not found",
			command:        "gwd firmware update AA-BB-CC-DD-EE-FF http://example.com/firmware.zip",
			devId:          "AA-BB-CC-DD-EE-FF",
			expectedError:  "no such device AA-BB-CC-DD-EE-FF",
			expectedStatus: "error: device not found", // Updated to match actual output
		},
		{
			name:    "Device locked",
			command: "gwd firmware update 11-22-33-44-55-66 http://example.com/firmware.zip",
			devId:   "11-22-33-44-55-66",
			prepare: func() {
				dev := QC.DevData["11-22-33-44-55-66"]
				dev.Lock = true
				QC.DevData["11-22-33-44-55-66"] = dev
			},
			expectedStatus: "info: device is updating",
		},
		{
			name:           "Invalid URL format",
			command:        "gwd firmware update 11-22-33-44-55-66 invalid-url",
			devId:          "11-22-33-44-55-66",
			expectedStatus: "info: device is updating",
		},
		{
			name:           "Unsupported protocol",
			command:        "gwd firmware update 11-22-33-44-55-66 ftp://example.com/firmware.zip",
			devId:          "11-22-33-44-55-66",
			expectedStatus: "info: device is updating",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.prepare != nil {
				tt.prepare()
			}

			cmd := &CmdInfo{
				Command: tt.command,
				DevId:   tt.devId,
			}
			_, err := IsAValidGwdFirmwareCmd(cmd)

			// Check error
			if tt.expectedError != "" {
				if err == nil {
					t.Fatalf("Expected error '%s', got nil", tt.expectedError)
				}
				if !strings.Contains(err.Error(), tt.expectedError) {
					t.Fatalf("Expected error containing '%s', got '%v'", tt.expectedError, err)
				}
			} else if err != nil {
				t.Fatalf("Expected no error, got: %v", err)
			}

			result := GwdFirmwareCmd(cmd)

			if !strings.Contains(result.Status, tt.expectedStatus) {
				t.Errorf("Expected status containing '%s', got '%s'", tt.expectedStatus, result.Status)
			}
		})
	}
}

// Test cases for edge cases
func TestGwdFirmwareCmd_RealEdgeCases(t *testing.T) {
	model := "EHG75xx"
	setupRealEnvironment(model)

	tests := []struct {
		name           string
		command        string
		devId          string
		expectedError  string
		expectedStatus string
	}{
		{
			name:           "Invalid command format (too few arguments)",
			command:        "gwd firmware update http://example.com/firmware.zip",
			devId:          "",
			expectedError:  "error: invalid command arguments, expected 5 but got 4",
			expectedStatus: "error: invalid command arguments, expected 5 but got 4",
		},
		{
			name:           "Valid command format with incomplete MAC",
			command:        "gwd firmware update 11-22-33 http://example.com/firmware.zip",
			devId:          "11-22-33",
			expectedError:  "invalid MAC address length",
			expectedStatus: "error: invalid MAC Address",
		},
		{
			name:           "Valid command format with invalid MAC format",
			command:        "gwd firmware update 11-22-33-44-55-HH http://example.com/firmware.zip",
			devId:          "11-22-33-44-55-HH",
			expectedError:  "invalid MAC Address",
			expectedStatus: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmd := &CmdInfo{
				Command: tt.command,
				DevId:   tt.devId,
			}
			_, err := IsAValidGwdFirmwareCmd(cmd)

			// Check error
			if tt.expectedError == "" {
				if err != nil {
					t.Fatalf("Expected no error, got: %v", err)
				}
			} else {
				if err == nil {
					t.Fatalf("Expected error '%s', got nil", tt.expectedError)
				}
				if !strings.Contains(err.Error(), tt.expectedError) {
					t.Fatalf("Expected error containing '%s', got '%v'", tt.expectedError, err)
				}
			}

			// Check status
			if tt.expectedStatus != "" && !strings.Contains(cmd.Status, tt.expectedStatus) {
				t.Errorf("Expected status containing '%s', got '%s'", tt.expectedStatus, cmd.Status)
			}
		})
	}
}
*/