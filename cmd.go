package mnms

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/url"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/asaskevich/govalidator"
	"github.com/qeof/q"
	"github.com/ziutek/telnet"
)

// ValidCommands is a list of valid commands
//
// Keep this list up to date
var ValidCommands []string = []string{
	"config", "mtderase", "beep", "reset", "scan", "switch", "snmp", "syslog", "gwd",
	"debug", "firmware", "mqtt", "opcua", "help", "util", "wg", "tcpproxy", "agent", "idps", "device",
	"msg", "ssh", "firewall", "service",
}

const (
	defaultUsername = "admin"
	defaultPassword = "default"
)

type extraCmd func(cmdinfo *CmdInfo) *CmdInfo

var IdpsCmd extraCmd

// CmdInfo contains a command, a unit of API call
//
// A command is a API call that is executed in a distributed environement.
//
// A command created by the user and inserted at Root may be replicated
// to Network services. A Network service that is capable of executing the command will
// run the command and return the result by reporting back to the Root.
type CmdInfo struct {
	Index       int    `json:"index"`
	Kind        string `json:"kind"` // root, agent, gwd, snmp, beep, reset, firmware, mtderase, config, service
	Timestamp   string `json:"timestamp"`
	Command     string `json:"command"`
	Result      string `json:"result"`
	Status      string `json:"status"`
	Retries     int    `json:"retries"`
	NoOverwrite bool   `json:"nooverwrite"`
	All         bool   `json:"all"`
	NoSyslog    bool   `json:"nosyslog"`
	Client      string `json:"client"`
	DevId       string `json:"devid"`
	Tag         string `json:"tag"`
	Edit        string `json:"edit"`
	Verify      string `json:"verify"`
}

type LastCmdsQuery struct {
	Count    int
	Duration string // Duration string, e.g. 1h, 2m, 3s
	Filter   string // Filter string, e.g. "error", "xxx"
}

const telnet_timeout = 10 * time.Second // XXX

func init() {
	QC.CmdData = make(map[string]CmdInfo)
	QC.CmdIndex = 1
}

// comman function for validate and find device
func ValidateAndFindDevice(macaddr string, cmdinfo *CmdInfo) (*CmdInfo, *DevInfo, error) {
	standardMac, err := IsValidMACAddress(macaddr)
	if err != nil {
		cmdinfo.Status = "error: invalid MAC Address"
		return cmdinfo, nil, err
	}
	cmdinfo.DevId = standardMac
	q.Q(standardMac)
	dev, err := FindDev(standardMac)
	if err != nil || dev == nil {
		cmdinfo.Status = "error: device not found"
		return cmdinfo, nil, err
	}
	if len(cmdinfo.Client) > 0 && dev.ScannedBy != cmdinfo.Client {
		cmdinfo.Status = "error: device is not scanned by the same client"
		return cmdinfo, nil, errors.New("device is not scanned by the same client")
	}
	return cmdinfo, dev, nil
}

// NormalizeMACAddresses replaces MAC addresses with colons to use dashes
func NormalizeMACAddresses(command string) string {
	macPattern := regexp.MustCompile(`([0-9A-Fa-f]{2}):([0-9A-Fa-f]{2}):([0-9A-Fa-f]{2}):([0-9A-Fa-f]{2}):([0-9A-Fa-f]{2}):([0-9A-Fa-f]{2})`)
	return macPattern.ReplaceAllString(command, "$1-$2-$3-$4-$5-$6")
}

// since agent.go use this function to validate
func ValidateCommand(cmd string) error {
	ws := strings.Fields(cmd)
	if len(ws) < 2 {
		return fmt.Errorf("error: invalid command, not enough fields")
	}
	for _, c := range ValidCommands {
		if c == ws[0] {
			return nil // TODO also check parameters
		}
	}
	return fmt.Errorf("error: unknown command, %s", ws[0])
}

func ValidateCommands(cmdinfo *CmdInfo) (*CmdInfo, error) {
	if err := IsAValidCommands(cmdinfo.Command); err != nil {
		cmdinfo.Status = err.Error()
		return cmdinfo, err
	}
	// Normalize MAC addresses before expanding KV values
	cmdinfo.Command = NormalizeMACAddresses(cmdinfo.Command)

	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo, err
	}
	cmdinfo.Command = cmd

	if cmdinfo.Kind == "root" {
		// root cmd will execute later
		return cmdinfo, nil
	}

	// agent command validation
	if strings.HasPrefix(cmd, "agent") {
		return IsAValidAgentCmd(cmdinfo)
	}
	// beep command validation
	if strings.HasPrefix(cmd, "beep") {
		return IsAValidBeepCmd(cmdinfo)
	}
	// reset command validation
	if strings.HasPrefix(cmd, "reset") {
		return IsAValidResetCmd(cmdinfo)
	}
	// firmware update command validation
	if strings.HasPrefix(cmd, "firmware update") {
		return IsAValidFWUpdateCmd(cmdinfo)
	}
	// validate gwd command
	if strings.HasPrefix(cmd, "gwd") {
		return ValidateGwdCmd(cmdinfo)
	}
	// validate all config command
	if strings.HasPrefix(cmd, "config") {
		return ValidateConfigCmd(cmdinfo)
	}
	// validate snmp high level command
	if strings.HasPrefix(cmd, "snmp") {
		return ValidateSnmpCmd(cmdinfo)
	}
	// validate service command
	if strings.HasPrefix(cmd, "service") {
		return IsAValidServiceCmd(cmdinfo)
	}
	// mtderase command validation
	if strings.HasPrefix(cmd, "mtderase") {
		return IsAValidMtdEraseCmd(cmdinfo)
	}
	// mtderase command validation
	if strings.HasPrefix(cmd, "device edit") {
		return IsAValidDeviceEditCmd(cmdinfo)
	}
	if strings.HasPrefix(cmd, "device delete") {
		return IsAValidDeviceDeleteCmd(cmdinfo)
	}
	return cmdinfo, nil
}

func ValidateSnmpCmd(cmdinfo *CmdInfo) (*CmdInfo, error) {
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	if len(ws) < 3 {
		q.Q("error", len(ws))
		cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 3 or more but got %d", len(ws))
		return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 3 or more but got %d", len(ws))
	}
	// snmp [enable/disable] [mac address]
	if ws[1] == "enable" || ws[1] == "disable" {
		if len(ws) != 3 {
			cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 3 but got %d", len(ws))
			return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 3 but got %d", len(ws))
		}
		macaddr := ws[2]
		standardMac, err := IsValidMACAddress(macaddr)
		if err != nil {
			cmdinfo.Status = "error: invalid MAC Address"
			return cmdinfo, err
		}
		cmdinfo.DevId = standardMac
		dev, err := FindDev(standardMac)
		if err != nil {
			cmdinfo.Status = "error: device not found"
			return cmdinfo, err
		}
		_, ok := dev.Capabilities["agent"]
		if ok {
			if ws[1] == "enable" {
				agentCmd := fmt.Sprintf("agent snmp enable %s 1", standardMac)
				cmdinfo.Command = agentCmd
			} else {
				agentCmd := fmt.Sprintf("agent snmp enable %s 0", standardMac)
				cmdinfo.Command = agentCmd
			}
		} else {
			if ws[1] == "enable" {
				newcmd := fmt.Sprintf("switch %s snmp enable", standardMac)
				cmdinfo.Timestamp = time.Now().Format(time.RFC3339)
				cmdinfo.Command = newcmd

			} else {
				newcmd := fmt.Sprintf("switch %s no snmp enable", standardMac)
				cmdinfo.Timestamp = time.Now().Format(time.RFC3339)
				cmdinfo.Command = newcmd
			}
		}
		return cmdinfo, nil
	}
	// snmp trap [options] [mac address] [server ip] [server port] [community]
	if ws[1] == "trap" && (ws[2] == "add" || ws[2] == "del") {
		if len(ws) != 7 {
			q.Q("error", len(ws))
			cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 7 but got %d", len(ws))
			return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 7 but got %d", len(ws))
		}
		options := ws[2]
		devId := ws[3]
		serverIp := ws[4]
		serverPort := ws[5]
		community := ws[6]

		standardMac, err := IsValidMACAddress(devId)
		if err != nil {
			cmdinfo.Status = "error: invalid MAC Address"
			return cmdinfo, err
		}
		cmdinfo.DevId = standardMac
		dev, err := FindDev(standardMac)
		if err != nil {
			cmdinfo.Status = "error: device not found"
			return cmdinfo, err
		}
		err = CheckDeviceLock(standardMac)
		if err != nil {
			cmdinfo.Status = fmt.Sprintf("error: %v", err)
			return cmdinfo, err
		}
		// validate trap server ip
		err = CheckIPAddress(serverIp)
		if err != nil {
			cmdinfo.Status = fmt.Sprintf("error: %v", err)
			return cmdinfo, err
		}
		// validate trap server port
		err = CheckServerPortValidity(serverPort)
		if err != nil {
			cmdinfo.Status = fmt.Sprintf("error: %v", err)
			return cmdinfo, err
		}
		// make command as per capabilities

		isAgentContain := dev.Capabilities["agent"]
		if isAgentContain {
			// agent snmp trap [trap option] [mac address] [server ip] [server port] [community]
			agentCmd := fmt.Sprintf("agent snmp trap %s %s %s %s %s", options, standardMac, serverIp, serverPort, community)
			cmdinfo.Command = agentCmd
		} else { // if agent not found in capabilities
			// switch [mac address] snmp trap [server ip] [community] [server port]
			newCmd := fmt.Sprintf("switch %s snmp trap %s %s %s", standardMac, serverIp, community, serverPort)
			cmdinfo.Command = newCmd

		}
		return cmdinfo, nil
	}

	// snmp trap get [mac address]
	if ws[1] == "trap" && ws[2] == "get" {
		if len(ws) != 4 {
			q.Q("error", len(ws))
			cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 4 but got %d", len(ws))
			return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 4 but got %d", len(ws))
		}
		devId := ws[3]
		standardMac, err := IsValidMACAddress(devId)
		if err != nil {
			cmdinfo.Status = "error: invalid MAC Address"
			return cmdinfo, err
		}
		cmdinfo.DevId = standardMac
		dev, err := FindDev(standardMac)
		if err != nil {
			cmdinfo.Status = "error: device not found"
			return cmdinfo, err
		}
		err = CheckDeviceLock(standardMac)
		if err != nil {
			cmdinfo.Status = fmt.Sprintf("error: %v", err)
			return cmdinfo, err
		}
		// make command as per capabilities

		isAgentContain := dev.Capabilities["agent"]
		if isAgentContain {
			// agent snmp trap get [mac address]
			agentCmd := fmt.Sprintf("agent snmp trap get %s", standardMac)
			cmdinfo.Command = agentCmd
		} else { // if agent not found in capabilities
			// switch [mac address] show snmp trap
			newCmd := fmt.Sprintf("switch %s show snmp trap", standardMac)
			cmdinfo.Command = newCmd

		}
		return cmdinfo, nil

	}

	// snmp config syslog set [MAC Address] [Enable] [Server IP] [Server Port] [Log Level] [Log to Flash]
	if ws[1] == "config" && ws[2] == "syslog" && ws[3] == "set" {
		if len(ws) != 10 {
			q.Q("error", len(ws))
			cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 10 but got %d", len(ws))
			return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 10 but got %d", len(ws))
		}
		devId := ws[4]
		status := ws[5]
		serverIp := ws[6]
		serverPort := ws[7]
		serverLevel := ws[8]
		logToFlash := ws[9]

		standardMac, err := IsValidMACAddress(devId)
		if err != nil {
			cmdinfo.Status = "error: invalid MAC Address"
			return cmdinfo, err
		}
		cmdinfo.DevId = standardMac
		_, err = FindDev(standardMac)
		if err != nil {
			cmdinfo.Status = "error: device not found"
			return cmdinfo, err
		}
		// Validate syslog status value enable/disable
		if status != "1" && status != "2" {
			cmdinfo.Status = "error: invalid syslog status value, must be 1 for enable or 2 for disable"
			return cmdinfo, fmt.Errorf("error: invalid syslog status value, must be 1 for enable or 2 for disable")
		}
		// validate server ip
		err = CheckIPAddress(serverIp)
		if err != nil {
			cmdinfo.Status = fmt.Sprintf("error: %v", err)
			return cmdinfo, err
		}
		// validate server port
		err = CheckServerPortValidity(serverPort)
		if err != nil {
			cmdinfo.Status = fmt.Sprintf("error: %v", err)
			return cmdinfo, err
		}
		// Validate log level
		err = CheckSyslogLoglevelValidity(serverLevel)
		if err != nil {
			cmdinfo.Status = fmt.Sprintf("error: %v", err)
			return cmdinfo, err
		}
		// Validate syslog logtoflash value enable/disable
		if logToFlash != "1" && logToFlash != "2" {
			cmdinfo.Status = "error: invalid LogToFlash value, must be 1 for enable or 2 for disable"
			return cmdinfo, fmt.Errorf("error: invalid LogToFlash value, must be 1 for enable or 2 for disable")
		}
		return cmdinfo, nil
	}
	// snmp config syslog get [MAC Address]
	if ws[1] == "config" && ws[2] == "syslog" && ws[3] == "get" {
		cmd := cmdinfo.Command
		ws := strings.Split(cmd, " ")
		if len(ws) != 5 {
			q.Q("error", len(ws))
			cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 5 but got %d", len(ws))
			return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 5 but got %d", len(ws))
		}
		devId := ws[4]
		standardMac, err := IsValidMACAddress(devId)
		if err != nil {
			cmdinfo.Status = "error: invalid MAC Address"
			return cmdinfo, err
		}
		cmdinfo.DevId = standardMac
		_, err = FindDev(standardMac)
		if err != nil {
			cmdinfo.Status = "error: device not found"
			return cmdinfo, err
		}
		return cmdinfo, nil
	}
	if ws[1] == "options" {
		q.Q(ws)
		if len(ws) != 6 {
			cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 6 but got %d", len(ws))
			return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 6 but got %d", len(ws))
		}
		_, err := strconv.Atoi(ws[2])
		if err != nil {
			cmdinfo.Status = fmt.Sprintf("error: port %v", err)
			return cmdinfo, err
		}
		_, err = strconv.Atoi(ws[5])
		if err != nil {
			cmdinfo.Status = fmt.Sprintf("error: timeout %v", err)
			return cmdinfo, err
		}
		switch ws[4] {
		case "1", "2c", "3":
			return cmdinfo, nil
		default:
			cmdinfo.Status = fmt.Sprintf("error: version %v, accept 1|2c|3", err)
			return cmdinfo, fmt.Errorf("error: version %v, accept 1|2c|3", err)
		}
	}
	if ws[1] == "communities" {
		// read communities and write to DevInfo
		// snmp communities [mac]
		if len(ws) < 3 {
			cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 3 but got %d", len(ws))
			return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 3 but got %d", len(ws))
		}
		// find device
		devID := ws[2]
		standardMac, err := IsValidMACAddress(devID)
		if err != nil {
			cmdinfo.Status = "error: invalid MAC Address"
			return cmdinfo, err
		}
		cmdinfo.DevId = standardMac
		_, err = FindDev(standardMac)
		if err != nil {
			cmdinfo.Status = "error: device not found"
			return cmdinfo, err
		}
		return cmdinfo, nil
	}
	if ws[1] == "update" && ws[2] == "community" {
		if len(ws) != 6 {
			cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 6 but got %d", len(ws))
			return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 6 but got %d", len(ws))
		}
		mac := ws[3]
		standardMac, err := IsValidMACAddress(mac)
		if err != nil {
			cmdinfo.Status = "error: invalid MAC Address"
			return cmdinfo, err
		}
		cmdinfo.DevId = standardMac
		_, err = FindDev(standardMac)
		if err != nil {
			cmdinfo.Status = "error: device not found"
			return cmdinfo, err
		}
		return cmdinfo, nil
	}
	address := ws[2]
	// validate address
	err := CheckIPAddress(address)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo, err
	}
	return cmdinfo, nil
}

func IsAValidFWUpdateCmd(cmdinfo *CmdInfo) (*CmdInfo, error) {
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	if len(ws) != 4 {
		cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 4 but got %d", len(ws))
		return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 4 but got %d", len(ws))
	}
	macaddr := ws[2]
	file := ws[3]
	standardMac, err := IsValidMACAddress(macaddr)
	if err != nil {
		cmdinfo.Status = "error: invalid MAC Address"
		return cmdinfo, err
	}
	cmdinfo.DevId = standardMac
	q.Q(standardMac, file)
	dev, err := FindDev(standardMac)
	if err != nil {
		cmdinfo.Status = "error: device not found"
		return cmdinfo, err
	}

	// make command as per capabilities
	_, ok := dev.Capabilities["agent"]
	if ok {
		agentCmd := fmt.Sprintf("agent firmware update %s %s", standardMac, file)
		cmdinfo.Command = agentCmd
	} else {
		// if agent not found in capabilities
		// check special case
		if strings.Contains(dev.ModelName, "EHG2408") {
			cmdinfo.Status = fmt.Sprintf("error: %s device needs agent support for update firmware command", dev.ModelName)
			return cmdinfo, errors.New(cmdinfo.Status)
		}
		gwdCmd := fmt.Sprintf("gwd firmware update %s %s", standardMac, file)
		cmdinfo.Command = gwdCmd
	}
	return cmdinfo, nil
}

func ValidateGwdCmd(cmdinfo *CmdInfo) (*CmdInfo, error) {
	cmd := cmdinfo.Command
	q.Q("cmd = ", cmd)

	// gwd beep
	if strings.HasPrefix(cmd, "gwd beep") {
		return IsAValidGwdBeepCmd(cmdinfo)
	}
	if strings.HasPrefix(cmd, "gwd reset") {
		return IsAValidGwdResetCmd(cmdinfo)
	}
	// gwd config network
	if strings.HasPrefix(cmd, "gwd config") {
		return IsAValidGwdConfigCmd(cmdinfo)
	}
	if strings.HasPrefix(cmd, "gwd firmware update") {
		return IsAValidGwdFirmwareCmd(cmdinfo)
	}
	if strings.HasPrefix(cmd, "gwd mtderase") {
		return IsAValidGwdMtdEraseCmd(cmdinfo)
	}

	q.Q("unrecognized", cmd, len(cmd))
	cmdinfo.Status = "error: unknown command"
	return cmdinfo, fmt.Errorf("error: unknown command")
}

func ValidateConfigCmd(cmdinfo *CmdInfo) (*CmdInfo, error) {
	cmd := cmdinfo.Command
	if strings.HasPrefix(cmd, "config network") {
		return IsAValidConfigNetworkCmd(cmdinfo)
	}
	if strings.HasPrefix(cmd, "config user") {
		return IsAValidConfigUserCmd(cmdinfo)
	}
	if strings.HasPrefix(cmd, "config save") {
		return IsAValidConfigSaveCmd(cmdinfo)
	}
	// configuration syslog set
	if strings.HasPrefix(cmd, "config syslog set") {
		return IsAValidConfigSyslogSetCmd(cmdinfo)
	}
	// Configuration syslog get
	if strings.HasPrefix(cmd, "config syslog get") {
		return IsAValidConfigSyslogGetCmd(cmdinfo)
	}
	q.Q("unrecognized", cmd, len(cmd))
	cmdinfo.Status = "error: unknown command"
	return cmdinfo, fmt.Errorf("error: unknown command")
}

// validate config network set [mac] [ip] [new ip] [mask] [gateway] [hostname] [dhcp]
func IsAValidConfigNetworkCmd(cmdinfo *CmdInfo) (*CmdInfo, error) {
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	if len(ws) != 10 {
		q.Q("error", len(ws))
		cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 10 but got %d", len(ws))
		return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 10 but got %d", len(ws))
	}
	devId := ws[3]
	currentIp := ws[4]
	newip := ws[5]
	mask := ws[6]
	gateway := ws[7]
	hostname := ws[8]
	dhcp := ws[9]

	// validate and find device with mac address and return cmdinfo struct, device struct and error
	// since dev is requred to check the model name, so we need to validate and find device
	// and return cmdinfo struct, device struct and error
	cmdinfo, dev, err := ValidateAndFindDevice(devId, cmdinfo)
	if err != nil {
		return cmdinfo, err
	}

	err = CheckDeviceLock(cmdinfo.DevId)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo, err
	}
	// validate current ip
	err = CheckIPAddress(currentIp)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo, err
	}
	// validate new ip
	err = CheckIPAddress(newip)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo, err
	}
	// validate mask
	err = CheckIPAddress(mask)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo, err
	}
	// validate gateway
	err = CheckIPAddress(gateway)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo, err
	}
	// Validate DHCP value
	if dhcp != "0" && dhcp != "1" {
		cmdinfo.Status = "error: invalid DHCP value, must be 0 or 1"
		return cmdinfo, err
	}
	// make command as per capabilities
	_, ok := dev.Capabilities["agent"]
	if ok {
		// agent network config set [mac] [new ip] [mask] [gateway] [hostname] [dhcp]
		agentCmd := fmt.Sprintf("agent config network set %s %s %s %s %s %s", cmdinfo.DevId, newip, mask, gateway, hostname, dhcp)
		cmdinfo.Command = agentCmd
	} else { // if agent not found in capabilities
		// check special case
		if strings.Contains(dev.ModelName, "EHG2408") {
			cmdinfo.Status = fmt.Sprintf("error: %s device needs agent support for config network setting command", dev.ModelName)
			return cmdinfo, errors.New(cmdinfo.Status)
		}
		gwdCmd := fmt.Sprintf("gwd config network set %s %s %s %s %s %s", cmdinfo.DevId, currentIp, newip, mask, gateway, hostname)
		cmdinfo.Command = gwdCmd
	}
	return cmdinfo, nil
}

// validate command for config user [mac address] [username] [password]
func IsAValidConfigUserCmd(cmdinfo *CmdInfo) (*CmdInfo, error) {
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	if len(ws) != 5 {
		cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 5 but got %d", len(ws))
		return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 5 but got %d", len(ws))
	}
	macaddr := ws[2]
	username := ws[3]
	password := ws[4]

	// validate and find device with mac address and return cmdinfo struct, device struct and error
	cmdinfo, _, err := ValidateAndFindDevice(macaddr, cmdinfo)
	if err != nil {
		return cmdinfo, err
	}

	// Check if username is longer than 5 characters
	if len(username) < 5 {
		cmdinfo.Status = "error: username must be more than 5 characters"
		return cmdinfo, err
	}
	// Check if password is longer than 7 characters
	if len(password) < 7 {
		cmdinfo.Status = "error: password must be more than 7 characters"
		return cmdinfo, err
	}
	return cmdinfo, nil
}

// validate for command "config save [mac address]""
func IsAValidConfigSaveCmd(cmdinfo *CmdInfo) (*CmdInfo, error) {
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	if len(ws) != 3 {
		cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 3 but got %d", len(ws))
		return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 3 but got %d", len(ws))
	}
	macaddr := ws[2]

	// validate and find device with mac address and return cmdinfo struct, device struct and error
	// since dev is requred to check the model name, so we need to validate and find device
	// and return cmdinfo struct, device struct and error
	cmdinfo, dev, err := ValidateAndFindDevice(macaddr, cmdinfo)
	if err != nil {
		return cmdinfo, err
	}

	// make command as per capabilities
	_, ok := dev.Capabilities["agent"]
	if ok {
		agentCmd := fmt.Sprintf("agent config save %s", cmdinfo.DevId)
		cmdinfo.Command = agentCmd
	} else {
		// if agent not found in capabilities
		// check special case
		if strings.Contains(dev.ModelName, "EHG2408") {
			cmdinfo.Status = fmt.Sprintf("error: %s device needs agent support for save running config command", dev.ModelName)
			return cmdinfo, errors.New(cmdinfo.Status)
		}
		newcmd := fmt.Sprintf("switch config save %s", cmdinfo.DevId)
		cmdinfo.Command = newcmd
	}
	return cmdinfo, nil
}

// validate for command "config syslog set [mac address] [status] [server ip] [server port] [server level] [log to flash]"
func IsAValidConfigSyslogSetCmd(cmdinfo *CmdInfo) (*CmdInfo, error) {
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	if len(ws) != 9 {
		q.Q("error", len(ws))
		cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 9 but got %d", len(ws))
		return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 9 but got %d", len(ws))
	}
	devId := ws[3]
	status := ws[4] //Log to Server enable/disable
	serverIp := ws[5]
	serverPort := ws[6]
	serverLevel := ws[7]
	logToFlash := ws[8] //Log to Flash enable/disable

	// validate and find device with mac address and return cmdinfo struct, device struct and error
	// since dev is requred to check the model name, so we need to validate and find device
	// and return cmdinfo struct, device struct and error
	cmdinfo, dev, err := ValidateAndFindDevice(devId, cmdinfo)
	if err != nil {
		return cmdinfo, err
	}

	// Validate syslog status value enable/disable
	if status != "0" && status != "1" {
		cmdinfo.Status = "error: invalid syslog status value, must be 0 or 1"
		return cmdinfo, fmt.Errorf("error: invalid syslog status value, must be 0 or 1")
	}
	// validate server ip
	err = CheckIPAddress(serverIp)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo, err
	}
	// validate server port
	err = CheckServerPortValidity(serverPort)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo, err
	}
	// Validate log level
	err = CheckSyslogLoglevelValidity(serverLevel)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo, err
	}
	// Validate syslog logtoflash value enable/disable
	if logToFlash != "0" && logToFlash != "1" {
		cmdinfo.Status = "error: invalid LogToFlash value, must be 0 or 1"
		return cmdinfo, fmt.Errorf("error: invalid LogToFlash value, must be 0 or 1")
	}

	// make command as per capabilities
	_, ok := dev.Capabilities["agent"]
	if ok {
		// agent config syslog set [MAC Address] [Enable] [Server IP] [Server Port] [Log Level] [Log to Flash]
		agentCmd := fmt.Sprintf("agent config syslog set %s %s %s %s %s %s", cmdinfo.DevId, status, serverIp, serverPort, serverLevel, logToFlash)
		cmdinfo.Command = agentCmd
	} else {
		// for syslog setting via snmp will accept status(Log to Server) and logToFlash as 1(enable) or 2(disable) only
		var converted_status, converted_logToFlash string
		if status == "1" {
			converted_status = "1" // 1 means enable
		}
		if logToFlash == "1" {
			converted_logToFlash = "1" // 1 means enable
		}
		if status == "0" {
			converted_status = "2" // 2 means disable
		}
		if logToFlash == "0" {
			converted_logToFlash = "2" // 2 means disable
		}
		// if agent not found in capabilities
		// check special case
		if strings.Contains(dev.ModelName, "EHG2408") {
			cmdinfo.Status = fmt.Sprintf("error: %s device needs agent support for config syslog setting command", dev.ModelName)
			return cmdinfo, errors.New(cmdinfo.Status)
		}
		snmpCmd := fmt.Sprintf("snmp config syslog set %s %s %s %s %s %s", cmdinfo.DevId, converted_status, serverIp, serverPort, serverLevel, converted_logToFlash)
		cmdinfo.Command = snmpCmd

	}
	return cmdinfo, nil
}

// validate for command "config syslog get [mac address]"
func IsAValidConfigSyslogGetCmd(cmdinfo *CmdInfo) (*CmdInfo, error) {
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	if len(ws) != 4 {
		q.Q("error", len(ws))
		cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 4 but got %d", len(ws))
		return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 4 but got %d", len(ws))
	}
	devId := ws[3]

	// validate and find device with mac address and return cmdinfo struct, device struct and error
	cmdinfo, dev, err := ValidateAndFindDevice(devId, cmdinfo)
	if err != nil {
		return cmdinfo, err
	}

	// make command as per capabilities
	_, ok := dev.Capabilities["agent"]
	if ok {
		// agent config syslog get [MAC Address]
		agentCmd := fmt.Sprintf("agent config syslog get %s", cmdinfo.DevId)
		cmdinfo.Command = agentCmd
	} else {
		// if agent not found in capabilities
		// check special case
		if strings.Contains(dev.ModelName, "EHG2408") {
			cmdinfo.Status = fmt.Sprintf("error: %s device needs agent support for config syslog get command", dev.ModelName)
			return cmdinfo, errors.New(cmdinfo.Status)
		}
		// snmp config syslog get [MAC Address]
		snmpCmd := fmt.Sprintf("snmp config syslog get %s", cmdinfo.DevId)
		cmdinfo.Command = snmpCmd

	}
	return cmdinfo, nil
}

func IsAValidGwdFirmwareCmd(cmdinfo *CmdInfo) (*CmdInfo, error) {
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	if len(ws) != 5 {
		q.Q("error", len(ws))
		cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 5 but got %d", len(ws))
		return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 5 but got %d", len(ws))
	}
	devId := ws[3]

	// validate and find device with mac address and return cmdinfo struct, device struct and error
	// since dev is required to get ipaddress, so we need to validate and find device
	// and return cmdinfo struct, device struct and error
	cmdinfo, dev, err := ValidateAndFindDevice(devId, cmdinfo)
	if err != nil {
		return cmdinfo, err
	}

	ip := dev.IPAddress
	// validate ip
	err = CheckIPAddress(ip)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo, err
	}
	return cmdinfo, nil
}

func IsAValidGwdConfigCmd(cmdinfo *CmdInfo) (*CmdInfo, error) {
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	if len(ws) != 10 {
		q.Q("error", len(ws))
		cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 10 but got %d", len(ws))
		return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 10 but got %d", len(ws))
	}
	devId := ws[4]
	currentIp := ws[5]
	newip := ws[6]
	mask := ws[7]
	gateway := ws[8]

	// validate and find device with mac address and return cmdinfo struct, device struct and error
	cmdinfo, _, err := ValidateAndFindDevice(devId, cmdinfo)
	if err != nil {
		return cmdinfo, err
	}

	err = CheckIPAddress(currentIp)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo, err
	}
	// validate new ip
	err = CheckIPAddress(newip)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo, err
	}
	// validate mask
	err = CheckIPAddress(mask)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo, err
	}
	// validate gateway
	err = CheckIPAddress(gateway)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo, err
	}
	return cmdinfo, nil
}

// validate for command "reset [mac]"
func IsAValidResetCmd(cmdinfo *CmdInfo) (*CmdInfo, error) {
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	if len(ws) != 2 {
		cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 2 but got %d", len(ws))
		return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 2 but got %d", len(ws))
	}
	var macaddr string
	Unpack(ws[1:], &macaddr)

	// validate and find device with mac address and return cmdinfo struct, device struct and error
	// since dev is requred to check the model name, so we need to validate and find device
	// and return cmdinfo struct, device struct and error
	cmdinfo, dev, err := ValidateAndFindDevice(macaddr, cmdinfo)
	if err != nil {
		return cmdinfo, err
	}
	err = CheckDeviceLock(cmdinfo.DevId)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo, err
	}
	// make command as per capabilities
	isAgentContain := dev.Capabilities["agent"]
	if isAgentContain {
		// agent reset ipaddress not required
		agentCmd := fmt.Sprintf("agent reset %s", cmdinfo.DevId)
		cmdinfo.Command = agentCmd
	} else {
		// if agent not found in capabilities
		// check special case
		if strings.Contains(dev.ModelName, "EHG2408") {
			cmdinfo.Status = fmt.Sprintf("error: %s device needs agent support for reboot command", dev.ModelName)
			return cmdinfo, errors.New(cmdinfo.Status)
		}
		gwdCmd := fmt.Sprintf("gwd reset %s", cmdinfo.DevId)
		cmdinfo.Command = gwdCmd
	}
	return cmdinfo, nil
}

// validate for command "gwd reset [mac]"
func IsAValidGwdResetCmd(cmdinfo *CmdInfo) (*CmdInfo, error) {
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	if len(ws) != 3 {
		cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 3 but got %d", len(ws))
		return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 3 but got %d", len(ws))
	}
	macaddr := ws[2]
	// validate and find device with mac address and return cmdinfo struct, device struct and error
	cmdinfo, _, err := ValidateAndFindDevice(macaddr, cmdinfo)
	if err != nil {
		return cmdinfo, err
	}
	return cmdinfo, nil
}

// validate for command "agent beep [mac] and many"
func IsAValidAgentCmd(cmdinfo *CmdInfo) (*CmdInfo, error) {
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	if len(ws) < 3 {
		q.Q("error", len(ws))
		cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 3 or more but got %d", len(ws))
		return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 3 or more but got %d", len(ws))
	}
	if strings.HasPrefix(cmd, "agent openvpn keylist") {
		clientname := ws[3]
		QC.OpenvpnMutex.Lock()
		clientkeys, ok := QC.OpenvpnData[clientname]
		QC.OpenvpnMutex.Unlock()
		if !ok {
			cmdinfo.Status = "error: not found client name"
			return cmdinfo, fmt.Errorf("error: not found client name")
		}
		cmdinfo.Result = fmt.Sprintf("CaCert= %s;ClientCert= %s;ClientKey= %s;StaticKey= %s;Timestamp= %s\n",
			clientkeys.CaCert,
			clientkeys.ClientCert,
			clientkeys.ClientKey,
			clientkeys.StaticKey,
			clientkeys.Timestamp)
		cmdinfo.Status = "ok"
		return cmdinfo, nil
	}

	standardMac, err := FindMacWithCmd(ws[2:])
	if err != nil {
		cmdinfo.Status = "error: invalid MAC Address"
		return cmdinfo, err
	}
	cmdinfo.DevId = standardMac
	q.Q(standardMac)
	dev, err := FindDev(standardMac)
	if err != nil || dev == nil {
		cmdinfo.Status = "error: device not found"
		return cmdinfo, err
	}
	_, ok := dev.Capabilities["agent"]
	if !ok {
		cmdinfo.Status = "error: The agent client of the device did not start successfully."
		return cmdinfo, fmt.Errorf("error: The agent client of the device did not start successfully.")
	}
	return cmdinfo, nil
}

// validate for command "beep [mac]"
func IsAValidBeepCmd(cmdinfo *CmdInfo) (*CmdInfo, error) {
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	if len(ws) != 2 {
		cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 2 but got %d", len(ws))
		return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 2 but got %d", len(ws))

	}
	macaddr := ws[1]
	// validate and find device with mac address and return cmdinfo struct, device struct and error
	// since dev is requred to check the model name, so we need to validate and find device
	// and return cmdinfo struct, device struct and error
	cmdinfo, dev, err := ValidateAndFindDevice(macaddr, cmdinfo)
	if err != nil {
		return cmdinfo, err
	}

	// check special case
	if strings.Contains(dev.ModelName, "EHG2408") || strings.Contains(dev.ModelName, "EHG65") {
		cmdinfo.Status = fmt.Sprintf("error: %s device does not support beep", dev.ModelName)
		return cmdinfo, fmt.Errorf("error: %s device does not support beep", dev.ModelName)
	}
	// make command as per capabilities
	_, ok := dev.Capabilities["agent"]
	if ok {
		// agent beep ipaddress not required
		agentCmd := fmt.Sprintf("agent beep %s", cmdinfo.DevId)
		cmdinfo.Command = agentCmd
	} else {
		// if agent not found in capabilities
		gwdCmd := fmt.Sprintf("gwd beep %s", cmdinfo.DevId)
		cmdinfo.Command = gwdCmd
	}
	return cmdinfo, nil
}

// validate for command "gwd beep [mac]"
func IsAValidGwdBeepCmd(cmdinfo *CmdInfo) (*CmdInfo, error) {
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	if len(ws) != 3 {
		cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 3 but got %d", len(ws))
		return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 3 but got %d", len(ws))
	}
	macaddr := ws[2]
	// validate and find device with mac address and return cmdinfo struct, device struct and error
	// since dev is requred to check the model name, so we need to validate and find device
	// and return cmdinfo struct, device struct and error
	cmdinfo, dev, err := ValidateAndFindDevice(macaddr, cmdinfo)
	if err != nil {
		return cmdinfo, err
	}
	// check special case
	if strings.Contains(dev.ModelName, "EHG2408") || strings.Contains(dev.ModelName, "EHG65") {
		cmdinfo.Status = fmt.Sprintf("error: %s device does not support beep", dev.ModelName)
		return cmdinfo, fmt.Errorf("error: %s device does not support beep", dev.ModelName)
	}
	return cmdinfo, nil
}

func IsAValidServiceCmd(cmdinfo *CmdInfo) (*CmdInfo, error) {
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	if len(ws) != 2 {
		cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 2 but got %d", len(ws))
		return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 2 but got %d", len(ws))
	}
	if cmdinfo.Client == "" && cmdinfo.Kind != "root" {
		cmdinfo.Status = "error: client name is empty"
		return cmdinfo, fmt.Errorf("error: client name is empty")
	}
	return cmdinfo, nil
}

// validate for command "gwd mtderase [mac]"
func IsAValidGwdMtdEraseCmd(cmdinfo *CmdInfo) (*CmdInfo, error) {
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	if len(ws) != 3 {
		cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 3 but got %d", len(ws))
		return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 3 but got %d", len(ws))
	}
	macaddr := ws[2]
	// validate and find device with mac address and return cmdinfo struct, device struct and error
	// since dev is requred to check the model name, so we need to validate and find device
	// and return cmdinfo struct, device struct and error
	cmdinfo, dev, err := ValidateAndFindDevice(macaddr, cmdinfo)
	if err != nil {
		return cmdinfo, err
	}
	// check special case validate model
	if !CheckMtdEraseModel(dev.ModelName) {
		cmdinfo.Status = fmt.Sprintf("error: Model %s not supported this gwd mtderase command", dev.ModelName)
		return cmdinfo, err
	}
	// validate ipaddr
	ipaddr := dev.IPAddress
	err = CheckIPAddress(ipaddr)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo, err
	}
	return cmdinfo, nil
}

func IsAValidCommands(cmd string) error {
	ws := strings.Fields(cmd)
	if len(ws) < 2 {
		return fmt.Errorf("error: invalid command, not enough fields")
	}
	for _, c := range ValidCommands {
		if c == ws[0] {
			return nil // TODO also check parameters
		}
	}
	return fmt.Errorf("error: unknown command, %s", ws[0])
}

func InsertCmdInfo(cmdinfo CmdInfo) error {
	kcmd := cmdinfo.Command
	q.Q("inserting cmd info", kcmd, cmdinfo)
	QC.CmdMutex.Lock()
	_, ok := QC.CmdData[kcmd]
	QC.CmdMutex.Unlock()
	if ok {
		q.Q("cmd exists", cmdinfo)
		if cmdinfo.NoOverwrite {
			q.Q("error: cmd exists, no-overwrite flag", cmdinfo)
			return fmt.Errorf("error: cmd exists and no-overwrite flag")
		}
		q.Q("will overwrite cmd", cmdinfo)
	}
	if cmdinfo.Timestamp == "" {
		cmdinfo.Timestamp = time.Now().Format(time.RFC3339)
	}

	QC.CmdMutex.Lock()
	QC.CmdIndex++
	cmdinfo.Index = QC.CmdIndex
	QC.CmdData[kcmd] = cmdinfo
	QC.CmdMutex.Unlock()
	return nil
}

// InsertCmd inserts command information into command data list.
//
// It is called by  UpdateCmds when commands are posted via http.
func InsertCmd(cmdinfo CmdInfo) error {
	q.Q(cmdinfo)
	if cmdinfo.Command == "" {
		q.Q("error: empty command", cmdinfo)
		return fmt.Errorf("error: empty command")
	}

	if cmdinfo.Kind == "root" {
		cmdinfo = *runRootCmd(&cmdinfo)
	}

	// check whether client existed
	if len(cmdinfo.Client) != 0 && cmdinfo.Kind != "root" {
		QC.ClientMutex.Lock()
		_, ok := QC.Clients[cmdinfo.Client]
		if !ok {
			cmdinfo.Status = fmt.Sprintf("error: client %v not existed", cmdinfo.Client)
		}
		QC.ClientMutex.Unlock()
	}

	return InsertCmdInfo(cmdinfo)
}

// InsertDownCmds puts downloaded command data into local CmdData[].
//
// Root maintains its own command data list.  Each client node service
// downloads commands from the Root periodically.
//
// InsertDownCmds is called by CheckCmds which is periodically called
// from the main service go routine.
func InsertDownCmds(cmddata *map[string]CmdInfo) error {
	q.Q("insert downloaded cmds", cmddata)

	for k, v := range *cmddata {
		QC.CmdMutex.Lock()
		c, ok := QC.CmdData[k]
		QC.CmdMutex.Unlock()
		if ok {
			if v.NoOverwrite {
				q.Q("error: no overwrite dnwlded cmd", v)
				continue
			}

			if c.Status == "ok" || c.Status == "error" {
				q.Q("error: can't overwrite completed cmd", v)
				continue
			}
		}
		err := InsertCmdInfo(v)
		if err != nil {
			q.Q("error: can't insert downloaded cmd", v)
			return err
		}

		q.Q("inserted downloaded cmd", v)
	}
	return nil
}

// maskString masks the input string according to the specified rules.
// - For length 1: Replace with "*"
// - For length 2: Keep the first character, mask the last
// - For length 3: Keep the first and last character, mask the middle
// - For length 4-6: Keep the first and last character, mask the middle
// - For length 7-10: Keep the first 2 and last 2 characters, mask the middle
// - For length 11 and above: Keep the first 3 and last 3 characters, mask the middle
func maskString(s string) string {
	n := len(s)

	switch {
	case n == 1:
		return "*"
	case n == 2:
		return s[:1] + "*"
	case n == 3:
		return s[:1] + "*" + s[2:]
	case n >= 4 && n <= 6:
		numCharsToKeep := 1
		start := s[:numCharsToKeep]
		end := s[n-numCharsToKeep:]
		middle := strings.Repeat("*", n-2*numCharsToKeep)
		return start + middle + end
	case n >= 7 && n <= 10:
		numCharsToKeep := 2
		start := s[:numCharsToKeep]
		end := s[n-numCharsToKeep:]
		middle := strings.Repeat("*", n-2*numCharsToKeep)
		return start + middle + end
	case n >= 11:
		numCharsToKeep := 3
		start := s[:numCharsToKeep]
		end := s[n-numCharsToKeep:]
		middle := strings.Repeat("*", n-2*numCharsToKeep)
		return start + middle + end
	default:
		// For any unexpected lengths, return the original string
		return s
	}
}

// ReplaceKeysInCmdInfo replaces the keys in the command info with the values from the KVStore API.
func ReplaceKeysInCmdInfo(cmdinfo *CmdInfo) (*CmdInfo, error) {
	command, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		return nil, err
	}
	if command != cmdinfo.Command {
		replacedCmdInfo := *cmdinfo
		QC.CmdMutex.Lock()
		cmdinfo.Status = "ok"

		cmdinfo.Result = "Replaced: " + cmdinfo.Command + " with " + command
		QC.CmdData[cmdinfo.Command] = *cmdinfo
		QC.CmdMutex.Unlock()
		replacedCmdInfo.Command = command
		return &replacedCmdInfo, nil
	}
	return cmdinfo, nil
}

func UpdateInvalidCmds(cmdList []CmdInfo) error {
	for _, v := range cmdList {
		err := InsertCmd(v)
		if err != nil {
			q.Q(err)
			return err
		}
		jsonBytes, err := json.Marshal(v)
		if err != nil {
			q.Q("error: can't marshal a cmd", err)
			return err
		}
		if v.Tag == "" {
			err = SendSyslog(LOG_NOTICE, "RunCmd", string(jsonBytes))
		} else {
			err = SendSyslog(LOG_NOTICE, v.Tag, string(jsonBytes))
		}
		if err != nil {
			q.Q("syslog err:", err)
		}
	}
	return nil
}

// UpdateCmds is called when clients post commands via http.
//
// CLI may post commands via http to allow command line access to API.
// An http client may post commands to send command via rest API.
//
// UpdateCmds will call InsertCmd to record the command
// into local command information list.
func UpdateCmds(cmdList []CmdInfo) error {
	q.Q("update cmds", cmdList)
	// if root is collecting command status from clients,
	// updates from each client will be aggregated.
	// update command status in the queue
	for _, v := range cmdList {
		// checks for delete command
		if v.Edit == "delete" {
			q.Q("skip update of edit,delete cmd", v)
			continue
		}
		q.Q("will update cmd", v)
		if v.Status == "" {
			err := InsertCmd(v)
			if err != nil {
				return err
			}
			continue
		}
		// fill in missing timestamp
		if v.Timestamp == "" {
			v.Timestamp = time.Now().Format(time.RFC3339)
		}
		if v.Index == 0 {
			QC.CmdIndex++
			v.Index = QC.CmdIndex
		}
		isExisted := false
		QC.CmdMutex.Lock()
		for kcd, vcd := range QC.CmdData {
			if vcd.Command == v.Command && (vcd.Client == v.Client || vcd.Client == "") {
				QC.CmdData[kcd] = v
				q.Q("cmd updated", vcd, v)
				isExisted = true
				break
			}
		}
		// do not append new commands at the above which only works if map count is greater than 0
		if !isExisted {
			QC.CmdIndex++
			v.Index = QC.CmdIndex
			QC.CmdData[v.Command] = v
		}
		QC.CmdMutex.Unlock()
	}
	return nil
}

// CheckCmds runs in client node services and periodically
// download commands from the Root service and run commands and
// update the results back to the Root service.
func CheckCmds() error {
	q.Q("check cmds")
	if QC.RootURL == "" {
		q.Q("warning: root URL is not set, cannot download and run cmds")
		return nil
	}
	// if there is a URL to the upper level, download commands
	// all clients + self
	queryParams := "?"
	QC.ClientMutex.Lock()
	for _, c := range QC.Clients {
		queryParams += "id=" + c.Name + "&"
	}
	QC.ClientMutex.Unlock()

	//
	queryParams += "id=" + QC.Name + "&"
	queryParams += "kind=" + QC.Kind
	resp, err := GetWithToken(QC.RootURL+"/api/v1/commands"+queryParams, QC.AdminToken)
	if err != nil {
		return err
	}
	if resp != nil {
		defer resp.Body.Close()
		cmddata := make(map[string]CmdInfo)
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			q.Q("error: cannot read resp body", err)
			return err
		}
		err = json.Unmarshal(body, &cmddata)
		if err != nil {
			q.Q("error: cannot unmarshal resp body", err)
			return err
		}
		if len(cmddata) > 0 {
			q.Q("downloaded cmds", cmddata)
		}
		// q.Q("downloaded cmds", cmddata)
		if len(cmddata) > 0 {
			InsertDownCmds(&cmddata)
		}
	}
	// convert a map of cmdInfo to a list of cmdInfo to run in order

	var cmdList []CmdInfo
	QC.CmdMutex.Lock()
	for _, v := range QC.CmdData {
		if v.Status != "" {
			continue
		}
		if v.Client != QC.Name && v.Client != "" {
			// skip commands not for this client
			continue
		}
		cmdList = append(cmdList, v)
	}
	q.Q("unsorted", cmdList)
	QC.CmdMutex.Unlock()
	// sort commands in the the order they were created
	sort.Slice(cmdList, func(i, j int) bool {
		return cmdList[i].Index < cmdList[j].Index
	})
	q.Q("sorted", cmdList)
	// run commands in the order they were created
	for _, ci := range cmdList {
		q.Q("running cmd in listed order", ci)
		// N.B. RunCmd() is NOT running with Mutex Lock
		// ci.Command = ParseCommand(ci.Command)
		res := RunCmd(&ci)
		kcmd := ci.Command
		QC.CmdMutex.Lock()
		QC.CmdData[kcmd] = *res
		QC.CmdMutex.Unlock()
		q.Q(res)
	}
	var cmdResList []CmdInfo
	QC.CmdMutex.Lock()
	for _, v := range QC.CmdData {
		if v.Status == "" {
			continue
		}
		if strings.HasPrefix(v.Status, "error:") && len(v.DevId) > 0 {
			AddDevError(v.DevId, v.Status)
		}
		cmdResList = append(cmdResList, v)
	}
	q.Q("cmd result list", cmdResList)
	QC.CmdMutex.Unlock()
	jsonBytes, err := json.Marshal(cmdResList)
	if err != nil {
		q.Q("error: cannot marshal cmd result list", err)
		return err
	}
	// update results back to root
	resp, err = PostWithToken(QC.RootURL+"/api/v1/commands", QC.AdminToken, bytes.NewBuffer(jsonBytes))
	if err != nil {
		q.Q("error: cannot post command list to Root", err)
		return err
	}
	q.Q("posted command result list to root", cmdResList)
	if resp != nil {
		defer resp.Body.Close()
		bodyText, err := io.ReadAll(resp.Body)
		if err != nil {
			q.Q("error: cannot read resp body", err)
			return err
		}
		cmdResp := []CmdInfo{}
		err = json.Unmarshal(bodyText, &cmdResp)
		if err != nil {
			q.Q("error: cannot unmarshal cmd response list", err)
			return err
		}
		q.Q("response from root after posting cmd res list", cmdResp)
		for _, v := range cmdResp {
			// delete ok and error commands from local memory
			if v.Status == "" {
				continue
			}

			// deleting cmd form network services after post if status "ok" or "error"
			// since we removed name and using client for name buildKey function is createing issue
			for kvcd, vcd := range QC.CmdData {
				if vcd.Command == v.Command && (vcd.Status == "ok" || strings.HasPrefix(vcd.Status, "error:")) {
					q.Q("check cmd, after posting, deleting cmd ", kvcd, v)
					QC.CmdMutex.Lock()
					delete(QC.CmdData, kvcd)
					QC.CmdMutex.Unlock()
				}
			}
		}
	}
	q.Q("done checkCmds", QC.CmdData)
	return nil
}

func sendCmdSyslog(tag string, cmdinfo *CmdInfo) {
	if cmdinfo.Status != "" && !cmdinfo.NoSyslog {
		jsonBytes, err := json.Marshal(cmdinfo)
		if err != nil {
			q.Q(err)
		}
		if cmdinfo.Tag == "" {
			err = SendSyslog(LOG_NOTICE, tag, string(jsonBytes))
		} else {
			err = SendSyslog(LOG_NOTICE, cmdinfo.Tag, string(jsonBytes))
		}
		q.Q("sending runcmd syslog", string(jsonBytes))
		if err != nil {
			q.Q("error: sending syslog", err)
		}
	}
}

func RunCmd(cmdinfo *CmdInfo) *CmdInfo {
	defer sendCmdSyslog("RunCmd", cmdinfo)

	q.Q("running cmd", cmdinfo)
	if strings.HasPrefix(cmdinfo.Status, "error:") {
		q.Q("error: cmd Status already error, will not run")
		return cmdinfo
	}
	cmd := cmdinfo.Command
	if !cmdinfo.All && cmdinfo.Client != "" {
		if cmdinfo.Client != QC.Name {
			q.Q("error: client name mismatch", cmdinfo.Client, QC.Name)
			return cmdinfo
		}
	}
	if len(cmdinfo.DevId) > 0 {
		dev, err := FindDev(cmdinfo.DevId)
		if err != nil || dev == nil {
			q.Q("error: cannot find device", cmdinfo.DevId)
			return cmdinfo
		} else if dev.ScannedBy != QC.Name {
			q.Q("scanned by mismatch, pass", dev.ScannedBy, QC.Name)
			return cmdinfo
		}
	} else if len(cmdinfo.Client) > 0 && cmdinfo.Client != QC.Name {
		q.Q("client name mismatch, pass", cmdinfo.Client)
		return cmdinfo
	}

	q.Q("run high level cmd", cmdinfo)

	cmdinfo.Client = QC.Name
	if strings.HasPrefix(cmd, "snmp") {
		return SnmpCmd(cmdinfo)
	}
	if strings.HasPrefix(cmd, "config") {
		return ConfigCmd(cmdinfo)
	}
	// end of running high level commands

	// Use extraCmds to run some cmds, e.g. idps.
	// we don't want extra dll of idps when
	// we build the mnms if import idpsystem pkg

	if IdpsCmd != nil && strings.HasPrefix(cmd, "idps") {
		return IdpsCmd(cmdinfo)
	}
	if strings.HasPrefix(cmd, "scan") {
		return ScanCmd(cmdinfo)
	}
	if strings.HasPrefix(cmd, "gwd") {
		return GwdCmd(cmdinfo)
	}
	if strings.HasPrefix(cmd, "switch") {
		return SwitchCmd(cmdinfo)
	}
	if strings.HasPrefix(cmd, "debug") {
		return DebugCmd(cmdinfo)
	}
	if strings.HasPrefix(cmd, "mqtt") {
		return RunMqttCmd(cmdinfo)
	}
	if strings.HasPrefix(cmd, "agent") {
		return RunAgentCmd(cmdinfo)
	}
	if strings.HasPrefix(cmd, "syslog") {
		return SyslogCmd(cmdinfo)
	}

	if strings.HasPrefix(cmd, "opcua") {
		ws := strings.Split(cmd, " ")
		if len(ws) < 2 {
			cmdinfo.Status = "error: invalid command"
			return cmdinfo
		}
		switch ws[1] {
		case "connect":
			return OpcuaConnectCmd(cmdinfo)
		case "read":
			return OpcuaReadCmd(cmdinfo)
		case "browse":
			return OpcuaBrowseReferenceCmd(cmdinfo)
		case "sub":
			return OpcuaSubscribeCmd(cmdinfo)
		case "deletesub":
			return OpcuDeleteSubscribeCmd(cmdinfo)
		case "close":
			return OpcuCloseCmd(cmdinfo)
		}
	}
	if strings.HasPrefix(cmd, "wg") {
		return WgCmd(cmdinfo)
	}
	if strings.HasPrefix(cmd, "tcpproxy") {
		return TcpProxyCmd(cmdinfo)
	}

	if strings.HasPrefix(cmd, "msg") {
		return MsgCmd(cmdinfo)
	}

	if strings.HasPrefix(cmd, "ssh") {
		return SshCmd(cmdinfo)
	}

	if strings.HasPrefix(cmd, "firewall") {
		return FirewallCmd(cmdinfo)
	}

	if strings.HasPrefix(cmd, "device") {
		ws := strings.Split(cmd, " ")
		if len(ws) < 2 {
			cmdinfo.Status = "error: invalid command"
			return cmdinfo
		}
		switch ws[1] {
		case "delete":
			return DeleteDevice(cmdinfo)

		case "edit":
			return EditDevice(cmdinfo)
		}
	}

	if strings.HasPrefix(cmd, "service") {
		return ServiceCmd(cmdinfo)
	}

	q.Q("unrecognized", cmd, len(cmd))
	cmdinfo.Status = "error: invalid command"
	return cmdinfo
}

// Beep target device.
// low level command (Gwd)
//
// Usage : gwd beep [mac address]
//
//	[mac address] : target device mac address
//
// Example :
//
//	gwd beep AA-BB-CC-DD-EE-FF
func GwdBeepCmd(cmdinfo *CmdInfo) *CmdInfo {
	dev, err := FindDev(cmdinfo.DevId)
	if err != nil || dev == nil {
		cmdinfo.Status = "error: device not found"
		return cmdinfo
	}
	ipaddr := dev.IPAddress
	err = GwdBeep(ipaddr, cmdinfo.DevId)
	if err != nil {
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	cmdinfo.Status = "ok"
	return cmdinfo
}

// Reset/Reboot target device.
// low level command
// Usage : gwd reset [mac address]
//
//	[mac address] : target device mac address

// Example : gwd reset AA-BB-CC-DD-EE-FF
func GwdResetCmd(cmdinfo *CmdInfo) *CmdInfo {
	standardMac := cmdinfo.DevId
	dev, err := FindDev(standardMac)
	if err != nil || dev == nil {
		cmdinfo.Status = "error: device not found"
		return cmdinfo
	}
	// Get the username and password
	username, password := GetCredentials(dev)
	err = CheckDeviceLock(standardMac)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo
	}
	ipaddr := dev.IPAddress
	err = GwdReset(ipaddr, standardMac, username, password)
	if err != nil {
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	cmdinfo.Status = "ok"
	return cmdinfo
}

// Use gwd to configure network setting.
//
// Usage : gwd config network set [mac address] [current ip] [new ip] [mask] [gateway] [hostname]
//
//	[mac address] : target device mac address
//	[current ip]  : target device current ip address
//	[new ip]      : target device would modify ip address
//	[mask]        : target device network mask
//	[gateway]     : target device gateway
//	[hostname]    : target device host name
//
// Example :
// gwd config network set AA-BB-CC-DD-EE-FF ********* ********* ************* 0.0.0.0 switch
// DHCP enable case:
// gwd config network set AA-BB-CC-DD-EE-FF ********* 0.0.0.0 ************* 0.0.0.0 switch
func GwdConfigNetworkCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	standardMac := cmdinfo.DevId
	ws := strings.Split(cmd, " ")
	currentIp := ws[5]
	newip := ws[6]
	mask := ws[7]
	gateway := ws[8]
	hostname := ws[9]
	dev, err := FindDev(standardMac)
	if err != nil || dev == nil {
		cmdinfo.Status = "error: device not found"
		return cmdinfo
	}
	err = CheckDeviceLock(standardMac)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo
	}
	// check ip existed
	if currentIp != newip && newip != "0.0.0.0" {
		r, err := ARPCheckExisted(newip)
		if err != nil {
			q.Q(err)
			cmdinfo.Status = fmt.Sprintf("error: %v", err)
			return cmdinfo
		}
		if r {
			q.Q(ErrorDeviceExisted)
			cmdinfo.Status = fmt.Sprintf("error: %v", ErrorDeviceExisted)
			return cmdinfo
		}
	}
	// TODO add group support if FindDev fails
	n := getGwdNetConfig(dev)
	n.NewIPAddress = newip
	n.Netmask = mask
	n.Gateway = gateway
	n.Hostname = hostname
	err = GwdConfig(n)
	if err != nil {
		q.Q(err)
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo
	}
	cmdinfo.Status = "ok"
	return cmdinfo
}

// Erase target device mtd and restore default settings.
//
// Usage :gwd mtderase [mac address]
//
//	[mac address] : target device mac address
//
// Example :
//
//	gwd mtderase AA-BB-CC-DD-EE-FF
func GwdMtdEraseCmd(cmdinfo *CmdInfo) *CmdInfo {
	dev, err := FindDev(cmdinfo.DevId)
	if err != nil || dev == nil {
		cmdinfo.Status = "error: device not found"
		return cmdinfo
	}
	// Get the username and password
	username, password := GetCredentials(dev)
	err = GwdMtdErase(dev.IPAddress, dev.Mac, username, password)
	if err != nil {
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	cmdinfo.Status = "ok"
	return cmdinfo
}

// validate for command "mtderase [mac]"
func IsAValidDeviceEditCmd(cmdinfo *CmdInfo) (*CmdInfo, error) {
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	if len(ws) != 6 {
		cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 6 but got %d", len(ws))
		return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 6 but got %d", len(ws))

	}
	macaddr := ws[2]
	// validate and find device with mac address and return cmdinfo struct, device struct and error
	// since dev is requred to check the model name, so we need to validate and find device
	// and return cmdinfo struct, device struct and error
	cmdinfo, _, err := ValidateAndFindDevice(macaddr, cmdinfo)
	if err != nil {
		return cmdinfo, err
	}
	// validate ipaddr
	netmask := ws[4]
	err = CheckIPAddress(netmask)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo, err
	}
	return cmdinfo, nil
}

// validate for command "device delete all/[mac]"
func IsAValidDeviceDeleteCmd(cmdinfo *CmdInfo) (*CmdInfo, error) {
	// since device is deleted from root side and network services sending cmdinfo from network side with some status
	// so we need not allow to run again otherwise it will give error device not found
	if cmdinfo.Status != "" {
		return cmdinfo, nil
	}
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	if len(ws) != 3 {
		cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 3 but got %d", len(ws))
		return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 3 but got %d", len(ws))
	}
	arg := ws[2]
	if arg == "all" {
		// delete all devices from root side and return cmdinfo to delete all devices from network side
		QC.DevMutex.Lock()
		clear(QC.DevData)
		QC.DevMutex.Unlock()
		// fixed if device got clear then topology should also clear so as per updated device list topology also update
		QC.TopologyMutex.Lock()
		clear(QC.TopologyData)
		QC.TopologyMutex.Unlock()
		return cmdinfo, nil
	}
	// validate and find device with mac address and return cmdinfo struct, device struct and error
	// since dev is requred to check the model name, so we need to validate and find device
	// and return cmdinfo struct, device struct and error
	cmdinfo, _, err := ValidateAndFindDevice(arg, cmdinfo)
	if err != nil {
		return cmdinfo, err
	}
	if _, ok := QC.DevData[cmdinfo.DevId]; ok {
		// delete specific device from root side
		QC.DevMutex.Lock()
		delete(QC.DevData, cmdinfo.DevId)
		q.Q("delete device", cmdinfo.DevId)
		QC.DevMutex.Unlock()
		// fixed if device got delte then topology should also delete so as per updated device list topology also update
		QC.TopologyMutex.Lock()
		delete(QC.TopologyData, cmdinfo.DevId)
		q.Q("delete device from topology", cmdinfo.DevId)
		QC.TopologyMutex.Unlock()
	}
	return cmdinfo, nil
}

// validate for command "mtderase [mac]"
func IsAValidMtdEraseCmd(cmdinfo *CmdInfo) (*CmdInfo, error) {
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	if len(ws) != 2 {
		cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 2 but got %d", len(ws))
		return cmdinfo, fmt.Errorf("error: invalid command arguments, expected 2 but got %d", len(ws))

	}
	macaddr := ws[1]
	// validate and find device with mac address and return cmdinfo struct, device struct and error
	// since dev is requred to check the model name, so we need to validate and find device
	// and return cmdinfo struct, device struct and error
	cmdinfo, dev, err := ValidateAndFindDevice(macaddr, cmdinfo)
	if err != nil {
		return cmdinfo, err
	}
	err = CheckDeviceLock(cmdinfo.DevId)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo, err
	}
	// make command as per capabilities
	_, ok := dev.Capabilities["agent"]
	if ok {
		// agent beep ipaddress not required
		agentCmd := fmt.Sprintf("agent mtderase %s", cmdinfo.DevId)
		cmdinfo.Command = agentCmd
	} else {
		// if agent not found in capabilities
		// check special case
		if strings.Contains(dev.ModelName, "EHG2408") {
			cmdinfo.Status = fmt.Sprintf("error: %s device needs agent support for mtderase command", dev.ModelName)
			return cmdinfo, errors.New(cmdinfo.Status)
		}
		gwdCmd := fmt.Sprintf("gwd mtderase %s", cmdinfo.DevId)
		cmdinfo.Command = gwdCmd
	}
	return cmdinfo, nil
}

func CheckSwitchCliModel(modelname string) bool {
	cliSupportList := []string{
		"EHG6",
		"EHG7",
		"EHG9",
		"EMG8",
		"RHG9",
		"RHG7",
		"EH7",
		"Simu",
		"EHG24",
	}
	// check modelname start with cliSupportList,
	for _, v := range cliSupportList {
		// convert v and modelname to lower case
		mo := strings.ToLower(modelname)
		vv := strings.ToLower(v)
		if strings.HasPrefix(mo, vv) {
			q.Q(modelname)
			return true
		}

	}
	// if strings.HasPrefix(modelname, "EH7") || strings.HasPrefix(modelname, "EHG7") {
	// 	q.Q(modelname)
	// 	return true
	// }
	q.Q("switch cli not supported", modelname)
	return false
}

func CheckMtdEraseModel(modelname string) bool {
	supportList := []string{
		"EHG7",
		"EHG9",
		"EMG8",
		"RHG9",
		"RHG7",
		"EH7",
		"Simu",
	}
	// check modelname start with supportList,
	for _, v := range supportList {
		// convert v and modelname to lower case
		mo := strings.ToLower(modelname)
		vv := strings.ToLower(v)
		if strings.HasPrefix(mo, vv) {
			q.Q(modelname)
			return true
		}

	}
	q.Q("switch not supported", modelname)
	return false
}

func ConvertSwitchCmd(modelname string, cmd []string) ([]string, error) {
	// get mode number
	number, err := regexp.Compile(`\d`)
	if err != nil {
		return cmd, nil
	}
	n := number.FindAllString(modelname, -1)

	// command base on EHG75xx
	switch {
	// ehg7xxx enable snmp:snmp enable, disable"no snmp enable
	case strings.HasPrefix(modelname, "EHG75"):
		return cmd, nil
	case strings.HasPrefix(modelname, "EHG6"):
		switch {
		case len(cmd) == 3:
			if cmd[0] == "no" && cmd[1] == "snmp" && cmd[2] == "enable" {
				cmd[1] = "snmp-globalActive"
				cmd[2] = "disable"
				cmd := cmd[1:]
				return cmd, nil
			}
		case len(cmd) == 2:
			if cmd[0] == "snmp" && cmd[1] == "enable" {
				cmd[0] = "snmp-globalActive"
				return cmd, nil
			}
		case len(cmd) == 5:
			if cmd[0] == "snmp" && cmd[1] == "trap" {
				cmd[1] = "snmp-trapAdd"
				cmd[3], cmd[4] = cmd[4], cmd[3]
				cmd := cmd[1:]
				return cmd, nil
			}
		}
	case strings.HasPrefix(modelname, "EHG24"):
		switch {
		case len(cmd) == 3:
			if cmd[0] == "no" && cmd[1] == "snmp" && cmd[2] == "enable" {
				cmd[1] = "snmp-globalActive"
				cmd[2] = "disable"
				cmd := cmd[1:] //snmp-globalActive disable
				return cmd, nil
			}
			if cmd[0] == "show" && cmd[1] == "snmp" && cmd[2] == "trap" {
				cmd[1] = "show"
				cmd[2] = "snmp-trap"
				cmd := cmd[1:] // show snmp-trap
				return cmd, nil
			}
		case len(cmd) == 2:
			if cmd[0] == "snmp" && cmd[1] == "enable" {
				cmd[0] = "snmp-globalActive"
				return cmd, nil // snmp-globalActive enable
			}
		case len(cmd) == 5: // snmp trap [server ip] [server port] [community]
			if cmd[0] == "snmp" && cmd[1] == "trap" {
				cmd[0] = "snmp-trap-add"
				cmd[1] = cmd[2]
				cmd[2] = cmd[4]
				cmd[3] = cmd[3]
				cmd := cmd[0:] // snmp-trap-add [server ip] [community] [server port]
				return cmd, nil
			}
		}
	case strings.HasPrefix(modelname, "EH7"):
		// EH7xxx enable snmp:snmp , disable:no snmp
		// judge wether is "no"
		if len(cmd) >= 1 {
			var snmp string
			switch cmd[0] {
			case "no":
				if len(cmd) >= 2 {
					snmp = cmd[1]
				}
			default:
				snmp = cmd[0]
			}
			switch snmp {
			case "snmp":
				cmd := deleteExtraSnmpCmd(cmd)
				return cmd, nil
			}
		}

		// for layer 3 command
	case len(n) >= 2 && n[1] == "6":
		if len(cmd) == 2 && cmd[0] == "show" && cmd[1] == "ip" {
			cmd[1] = "vlan"
			cmd = append(cmd, "ip", "address", "1")
			return cmd, nil
		}
	}
	return cmd, nil
}

func deleteExtraSnmpCmd(cmd []string) []string {
	rcmd := []string{}
	for _, v := range cmd {
		if v != "enable" {
			rcmd = append(rcmd, v)
		}
	}
	return rcmd
}

// Use target device CLI configuration commands.
//
// Usage : switch [mac address][cli cmd...]
//
//	[mac address] : target device mac address
//	[cli cmd...]  : target device cli command
//
// Example :
//
//	switch AA-BB-CC-DD-EE-FF show ip
//
// Usage : switch config save [mac address]
//
//	[mac address] : target device mac address
//
// Example :
//
//	switch config save AA-BB-CC-DD-EE-FF
func SwitchCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo
	}
	ws := strings.Split(cmd, " ")
	if len(ws) < 4 {
		q.Q("error", len(ws))
		cmdinfo.Status = "error: invalid command"
		return cmdinfo
	}
	var username, password string
	if ws[1] == "config" && ws[2] == "save" {
		if len(ws) < 4 {
			q.Q("error", len(ws))
			cmdinfo.Status = "error: invalid command"
			return cmdinfo
		} else if len(ws) > 4 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 4 but got %d", len(ws))
			return cmdinfo
		}
		devId := ws[3]
		standardMac, err := IsValidMACAddress(devId)
		if err != nil {
			cmdinfo.Status = "error: invalid MAC Address"
			return cmdinfo
		}
		cmdinfo.DevId = standardMac
		dev, err := FindDev(standardMac)
		if err != nil || dev == nil {
			cmdinfo.Status = "error: device not found"
			return cmdinfo
		}
		if dev.ModelName == "" {
			cmdinfo.Status = "error: invalid device model"
			return cmdinfo
		}
		if !CheckSwitchCliModel(dev.ModelName) {
			cmdinfo.Status = "error: switch cli not available"
			return cmdinfo
		}

		// Get the username and password
		username, password := GetCredentials(dev)

		if strings.Contains(dev.ModelName, "EHG75") ||
			strings.Contains(dev.ModelName, "EHG76") ||
			strings.Contains(dev.ModelName, "EMG") || strings.Contains(dev.ModelName, "RHG") {
			err = SendSwitch(cmdinfo, dev, username, password, "copy running-config startup-config", 2)
		} else {
			err = SendSwitch(cmdinfo, dev, username, password, "copy running-config startup-config", 1)
		}
		if err != nil {
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		cmdinfo.Status = "ok"
		return cmdinfo
	}
	devId := ws[1]
	standardMac, err := IsValidMACAddress(devId)
	if err != nil {
		cmdinfo.Status = "error: invalid MAC Address"
		return cmdinfo
	}
	dev, err := FindDev(standardMac)
	cmdinfo.DevId = standardMac
	if err != nil || dev == nil {
		cmdinfo.Status = "error: device not found"
		return cmdinfo
	}
	if dev.ModelName == "" {
		cmdinfo.Status = "error: invalid device model"
		return cmdinfo
	}

	if !CheckSwitchCliModel(dev.ModelName) {
		cmdinfo.Status = "error: switch cli not available"
		return cmdinfo
	}
	wcmd, err := ConvertSwitchCmd(dev.ModelName, ws[2:])
	if err != nil {
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	// Get the username and password
	username, password = GetCredentials(dev)

	// https://github.com/bbtechhive/mnms/pull/1057
	if strings.Contains(dev.ModelName, "EHG65") ||
		strings.Contains(dev.ModelName, "EHG24") {
		// variation 3, send enable and password: username special case
		err = SendSwitch(cmdinfo, dev, username, password, strings.Join(wcmd, " "), 3)
	} else {
		err = SendSwitch(cmdinfo, dev, username, password, strings.Join(wcmd, " "), 1)
	}
	if err != nil {
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	cmdinfo.Status = "ok"
	return cmdinfo
}

func expect(t *telnet.Conn, d ...string) error {
	q.Q(d)
	err := t.SetReadDeadline(time.Now().Add(telnet_timeout))
	if err != nil {
		q.Q(err)
		return err
	}
	err = t.SkipUntil(d...)
	if err != nil {
		q.Q(err)
		return err
	}
	return nil
}

func sendln(t *telnet.Conn, s string) {
	q.Q(s)
	err := t.SetWriteDeadline(time.Now().Add(telnet_timeout))
	if err != nil {
		q.Q(err)
		return
	}

	buf := make([]byte, len(s)+1)
	copy(buf, s)
	buf[len(s)] = '\n'
	_, err = t.Write(buf)
	if err != nil {
		q.Q(err)
	}
}

func SendSwitch(cmdinfo *CmdInfo, dev *DevInfo, username, password, cmd string, variation int) error {
	t, err := telnet.DialTimeout("tcp", dev.IPAddress+":23", telnet_timeout)
	if err != nil {
		q.Q(err)
		return err
	}
	defer func() {
		if err := t.Close(); err != nil {
			q.Q(err)
		}
	}()
	t.SetUnixWriteMode(true)

	err = expect(t, "sername: ")
	if err != nil {
		return err
	}
	sendln(t, username)
	err = expect(t, "assword: ")
	if err != nil {
		return err
	}
	sendln(t, password)
	_, err = parseAuthofTelnet(t)
	if err != nil {
		return err
	}

	// https://github.com/bbtechhive/mnms/pull/1057
	if variation == 3 {
		sendln(t, "enable")
		err = expect(t, "assword: ")
		if err != nil {
			return err
		}
		// we have to send username in response to
		// password prompt on these 'variation 3' devices
		sendln(t, username)
		err = expect(t, "#")
		if err != nil {
			return err
		}
	}
	if variation == 1 {
		sendln(t, "configure")
	}
	// variation 2 means do NOT send configure
	if variation == 3 {
		if !strings.Contains(cmd, "show") {
			sendln(t, "configure")
			err = expect(t, "#")
			if err != nil {
				return err
			}
		}
	}
	/*
		Not sure why we have to delete this else clause,
		but the test shows we have to,
		https://github.com/bbtechhive/mnms/pull/1057
		else {
			err = expect(t, "#")
			if err != nil {
				return err
			}
		}
	*/
	sendln(t, cmd)
	result, err := parseTelnetResult(t)
	if err != nil {
		q.Q(err)
		return err
	}
	cmdinfo.Result = result
	return nil
}

// Use different protocol to scan all devices.
//
// Usage : scan [protocol]
//
//	[protocol]    : use gwd/snmp to scan all devices.
//
// Example :
//
//	scan gwd
func ScanCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo
	}
	ws := strings.Split(cmd, " ")
	if len(ws) < 2 {
		cmdinfo.Status = "error: invalid command"
		return cmdinfo
	}
	if cmd == "scan gwd" {
		err := GwdInvite()
		if err != nil {
			q.Q(err)
			cmdinfo.Status = fmt.Sprintf("error: %v", err)
			return cmdinfo
		}
		cmdinfo.Status = "ok"
		return cmdinfo
	}
	if cmd == "scan snmp" {
		go func() {
			err := SnmpScan()
			if err != nil {
				q.Q(err)
			}
		}()
		cmdinfo.Status = "ok"
		return cmdinfo
	}
	// scan Range
	if strings.HasPrefix(cmd, "scan") {
		v := ws[1]
		if govalidator.IsCIDR(v) {
			return scanCidrCmd(cmdinfo)
		}
		return scanRangeCmd(cmdinfo)
	}

	cmdinfo.Status = "error: invalid command"
	return cmdinfo
}

// Use mqtt to publish/subscribe/unsubscribe/list topic.
//
// Usage : mqtt [mqttcmd] [tcp address] [topic] [data...]
//
//		[mqttcmd]     : pub/sub/unsub/list
//	                 list is show all subscribe topic
//		[tcp address] : would pub/sub/unsub broker tcp address
//		[topic]       : topic name
//		[data...]     : data is messages, only publish use it.
//
// Example :
//
//	mqtt pub ************:1883 topictest "this is messages."
//	mqtt sub ************:1883 topictest
//	mqtt unsub ************:1883 topictest
//	mqtt list
func RunMqttCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo
	}
	ws := strings.Split(cmd, " ")
	if len(ws) < 2 {
		q.Q("error", len(ws))
		cmdinfo.Status = "error: invalid command"
		return cmdinfo
	}
	selectOption := ws[1]
	if strings.HasPrefix(selectOption, "list") {
		result := DisplayAllSubscribeTopic()
		if result == "" {
			result = "not subscribe topic"
		}
		cmdinfo.Result = result
		cmdinfo.Status = "ok"
		return cmdinfo
	}
	tcpaddr := ws[2]
	checkIP := strings.Split(ws[2], ":")
	// pass "broker.emqx.io:1883" remote not address format
	// pass ":11883" local address
	if len(checkIP) < 2 {
		cmdinfo.Status = "error: tcp address invalid"
		return cmdinfo
	}
	//	The publish command of mqtt contains messages. And there are spaces
	//	in messages, the calculation of length will be incorrect.
	//	example :
	//		cmd =  mqtt pub ************:1883 topictest "this is messages."
	//		len(ws) = 7
	if len(ws) < 4 {
		q.Q("error", len(ws))
		cmdinfo.Status = "error: invalid command"
		return cmdinfo
	}
	topicname := ws[3]
	data := strings.Join(ws[4:], " ")
	if strings.HasPrefix(selectOption, "pub") {
		if data == "" {
			cmdinfo.Status = "error: invalid command"
			return cmdinfo
		}
		err := RunMqttPublish(tcpaddr, topicname, data)
		if err != nil {
			q.Q(err)
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		cmdinfo.Status = "ok"
		return cmdinfo
	}
	if strings.HasPrefix(selectOption, "sub") {
		err := RunMqttSubscribe(tcpaddr, topicname)
		if err != nil {
			q.Q(err)
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		cmdinfo.Status = "ok"
		return cmdinfo
	}
	if strings.HasPrefix(selectOption, "unsub") {
		err := RunMqttUnSubscribe(tcpaddr, topicname)
		if err != nil {
			q.Q(err)
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		cmdinfo.Status = "ok"
		return cmdinfo
	}
	cmdinfo.Status = "error: not command pub/sub"
	return cmdinfo
}

func parseTelnetResult(t *telnet.Conn) (string, error) {
	t.SetReadDeadline(time.Now().Add(time.Millisecond * 1000))
	var s strings.Builder
	for {
		b, err := t.ReadByte()
		if err != nil {
			break
		}
		_, err = s.WriteString(string(b))
		if err != nil {
			return "", err
		}
	}
	v := fmt.Sprintf("(?i)%v", string("Unknown"))
	r, err := regexp.Compile(v)
	if err != nil {
		return "", err
	}
	if r.MatchString(s.String()) {
		return "", errors.New("unknown command")
	}
	return s.String(), nil
}

func parseAuthofTelnet(t *telnet.Conn) (string, error) {
	t.SetReadDeadline(time.Now().Add(time.Millisecond * 1000))
	var s strings.Builder
	for {
		b, err := t.ReadByte()
		if err != nil {
			break
		}
		_, err = s.WriteString(string(b))
		if err != nil {
			return "", err
		}
	}
	v := fmt.Sprintf("(?i)%v", string("Fail"))
	r, err := regexp.Compile(v)
	if err != nil {
		return "", err
	}
	if r.MatchString(s.String()) {
		return "", errors.New("auth fail")
	}
	return s.String(), nil
}

// Use agent to control device.
// low level command (Agent)
//
// Usage : agent [command] [mac address] [value]
//
//	[mac address] : target device mac address
//	[command]     : refer doc/agent.md
//	[value]       : value to be set
//
// Example :
//
//	agent reset AA-BB-CC-DD-EE-FF
//	agent beep AA-BB-CC-DD-EE-FF
//	agent firmware update AA-BB-CC-DD-EE-FF https://*************/api/v1/files/xxxxxx.dld
//	agent agentclient upgrade AA-BB-CC-DD-EE-FF https://*************/api/v1/files/agentclient 12c0ced1a84158357525378ebcfa31967dd9bb3a32600602714bfe2222a1d609
//	agent snmp enable AA-BB-CC-DD-EE-FF 1
//	agent config syslog set AA-BB-CC-DD-EE-FF 1 ************* 5514 8 1
//	agent snmp trap add AA-BB-CC-DD-EE-FF ************* 5162 public
//	agent snmp trap del AA-BB-CC-DD-EE-FF ************* 5162 public
//	agent config network set AA-BB-CC-DD-EE-FF *********** ************* 0.0.0.0 switch 1
//	agent config network set AA-BB-CC-DD-EE-FF *********** ************* *********** switch 0
//	agent snmp trap get AA-BB-CC-DD-EE-FF
//	agent config save AA-BB-CC-DD-EE-FF
//	agent config syslog get AA-BB-CC-DD-EE-FF
//	agent devinfo send AA-BB-CC-DD-EE-FF
//	agent topologyinfo send AA-BB-CC-DD-EE-FF
//	agent openvpn set AA-BB-CC-DD-EE-FF server 1 udp 1194 0 0 2 0 0 ********
//	agent openvpn keys generate AA-BB-CC-DD-EE-FF TW Taiwan Taipei <NAME_EMAIL> vpn_client
//	agent openvpn keys upload AA-BB-CC-DD-EE-FF ca http://*************:27182/api/v1/files/ca.crt 3c933f3374c95451ea936df3bc009ed7df98f24cd0a09c40e6603d115d685e38
//	agent openvpn keys download AA-BB-CC-DD-EE-FF client1
//	agent openvpn keylist client1
//	agent openvpn status AA-BB-CC-DD-EE-FF start
//	agent ipsec set AA-BB-CC-DD-EE-FF 1 none none none 0 secrets 0 0 0 0 10800 0 0 0 0 3600 1 30 120
//	agent ipsec status AA-BB-CC-DD-EE-FF start
//	agent ssh reverse start AA-BB-CC-DD-EE-FF ******* 12345 443 22
//	agent ssh reverse stop AA-BB-CC-DD-EE-FF ******* 12345
//	agent ssh reverse status AA-BB-CC-DD-EE-FF
//	agent ssh reverse websrv AA-BB-CC-DD-EE-FF
//	agent ssh reverse connections AA-BB-CC-DD-EE-FF
//	agent config port enable AA-BB-CC-DD-EE-FF port1 1
//	agent config port enable AA-BB-CC-DD-EE-FF port1 0
//	agent config user add AA-BB-CC-DD-EE-FF daniel daniel admin
//	agent config user edit AA-BB-CC-DD-EE-FF daniel daniel default
//	agent config user del AA-BB-CC-DD-EE-FF daniel default
//	agent config gps enable AA-BB-CC-DD-EE-FF 1
//	agent portpwinfo send AA-BB-CC-DD-EE-FF
//	agent mtderase AA-BB-CC-DD-EE-FF
//	agent token refresh AA-BB-CC-DD-EE-FF
//	agent alarm port set AA-BB-CC-DD-EE-FF 2 1
//	agent alarm port set AA-BB-CC-DD-EE-FF 3 all
//	agent alarm status AA-BB-CC-DD-EE-FF
func RunAgentCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	if strings.HasPrefix(cmd, "agent openvpn keylist") {
		clientname := ws[3]
		QC.OpenvpnMutex.Lock()
		clientkeys, ok := QC.OpenvpnData[clientname]
		QC.OpenvpnMutex.Unlock()
		if !ok {
			cmdinfo.Status = "error: not found client name"
			return cmdinfo
		}
		cmdinfo.Result = fmt.Sprintf("CaCert= %s;ClientCert= %s;ClientKey= %s;StaticKey= %s;Timestamp= %s\n",
			clientkeys.CaCert,
			clientkeys.ClientCert,
			clientkeys.ClientKey,
			clientkeys.StaticKey,
			clientkeys.Timestamp)
		cmdinfo.Status = "ok"
		return cmdinfo
	}
	if strings.HasPrefix(cmd, "agent firmware update") {
		fileurl := ws[4]
		if !strings.HasPrefix(fileurl, "http") {
			cmdinfo.Status = "error: unknown file format"
			return cmdinfo
		}
	}
	return cmdinfo
}

// WaitCmdFinish wait for command finish
func WaitCmdFinish(ctx context.Context, cmd string, client string) (*CmdInfo, error) {
	cmdresult := make(chan CmdInfo)
	go func() {
		for {
			time.Sleep(time.Second * 1)
			select {
			case <-ctx.Done():
				return
			default:
				for _, c := range QC.CmdData {
					if c.Command == cmd && c.Client == client {
						if c.Status == "ok" || strings.HasPrefix(c.Status, "error") {
							cmdresult <- c
							return
						}
					}
				}

			}
		}
	}()
	select {
	case <-ctx.Done():
		return nil, fmt.Errorf("timeout")
	case info := <-cmdresult:
		// write info.Result
		if strings.HasPrefix(info.Status, "error") {
			return nil, fmt.Errorf("%s", info.Status)
		}
		return &info, nil
	}
}

// GetCmdResult get command result from url
func GetCmdResult(rooturl, cmd string) (*CmdInfo, error) {
	u, err := url.Parse(rooturl + "/api/v1/commands")
	if err != nil {
		return nil, err
	}
	query := u.Query()
	query.Set("cmd", cmd)
	u.RawQuery = query.Encode()
	q.Q(u.String())
	resp, err := GetWithToken(u.String(), QC.AdminToken)
	if err != nil {
		q.Q(err)
		return nil, err
	}
	if resp != nil {
		defer resp.Body.Close()
	}
	if resp.StatusCode != 200 {
		q.Q(err)
		return nil, fmt.Errorf("get command result fail, %d", resp.StatusCode)
	}

	cmddata := map[string]CmdInfo{}
	err = json.NewDecoder(resp.Body).Decode(&cmddata)
	if err != nil {
		return nil, err
	}
	q.Q(cmddata)
	for _, v := range cmddata {
		if v.Command == cmd {
			return &v, nil
		}
	}
	return nil, fmt.Errorf("can't find command result")
}

// QueryCmdTilFinished get command result from root server with context. It will wait for result until result is not empty or context.Done
func QueryCmdTilFinished(c context.Context, rooturl string, interval time.Duration, cmd string) (*CmdInfo, error) {
	if interval < time.Microsecond*100 {
		return nil, fmt.Errorf("interval is too short")
	}
	retChannel := make(chan *CmdInfo)
	go func() {
		for {
			ret, err := GetCmdResult(rooturl, cmd)
			if err != nil {
				time.Sleep(interval)
				continue
			}

			if ret.Status == "ok" || strings.HasPrefix(ret.Status, "error") {
				retChannel <- ret
				return
			}
			select {
			case <-c.Done():
				return
			default:
				time.Sleep(interval)
			}
		}
	}()
	select {
	case <-c.Done():
		return nil, c.Err()
	case r := <-retChannel:
		return r, nil
	}
}

func NormalizeCmdsSpaces(input string) string {
	input = strings.TrimSpace(input)
	input = strings.Join(strings.Fields(input), " ")
	return input
}

// Delete device.
//
// Usage : device delete all/[mac address]
//
//	[mac address] : target device mac address
//
// Example :
//
//	device delete all/AA-BB-CC-DD-EE-FF
func DeleteDevice(cmdinfo *CmdInfo) *CmdInfo {
	ws := strings.Split(cmdinfo.Command, " ")
	value := ws[2]
	q.Q(value)
	QC.DevMutex.Lock()
	if value == "all" {
		// delete all device from network services side so it can be update the device list
		clear(QC.DevData)
		// fixed if device got clear then topology should also clear so as per updated device list topology also update
		QC.TopologyMutex.Lock()
		clear(QC.TopologyData)
		QC.TopologyMutex.Unlock()
	} else {
		standardMac := cmdinfo.DevId
		// delete specific device from network services side
		delete(QC.DevData, standardMac)
		q.Q("delete device", standardMac)
		// fixed if device got delte then topology should also delete so as per updated device list topology also update
		QC.TopologyMutex.Lock()
		delete(QC.TopologyData, standardMac)
		q.Q("delete device from topology", standardMac)
		QC.TopologyMutex.Unlock()
	}
	QC.DevMutex.Unlock()
	cmdinfo.Status = "ok"
	return cmdinfo
}

// Edit device.
//
// Usage : device edit [mac address] [model] [netmask] [hostname]
//
//	[mac address] : target device mac address
//	[model] : target device model
//	[netmask] : target device netmask
//	[hostname] : target device hostname
//
// Example :
//
//	device edit AA-BB-CC-DD-EE-FF
func EditDevice(cmdinfo *CmdInfo) *CmdInfo {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo
	}
	ws := strings.Split(cmd, " ")
	mac := ws[2]

	QC.DevMutex.Lock()
	dev, ok := QC.DevData[mac]
	QC.DevMutex.Unlock()
	if ok {
		itme := editableItem{modelName: ws[3], netmask: ws[4], hostname: ws[5]}
		dev.Edit(itme)
		InsertAndPublishDevice(dev)
	}
	cmdinfo.Status = "ok"
	return cmdinfo
}

func PrettyPrint(i interface{}) {
	s, _ := json.MarshalIndent(i, "", "\t")
	fmt.Println(string(s))
}

// RetrieveLastCmds retrieve last commands from source, if source is empty, return last commands from QC.CmdData
func RetrieveLastCmds(query LastCmdsQuery, source ...map[string]CmdInfo) (map[string]CmdInfo, error) {
	q.Q(query, source)
	if len(source) == 0 {
		return RetrieveLastCmds(query, QC.CmdData)
	}

	if len(source) > 1 {
		return nil, fmt.Errorf("too many sources, expected 1 but got %d", len(source))
	}

	if len(source[0]) == 0 {
		return map[string]CmdInfo{}, nil
	}

	// sort by timestamp
	type CmdSlice struct {
		Key string
		Cmd CmdInfo
	}
	cmdSlice := []CmdSlice{}
	for k, v := range source[0] {
		cmdSlice = append(cmdSlice, CmdSlice{k, v})
	}
	sort.Slice(cmdSlice, func(i, j int) bool {
		return cmdSlice[i].Cmd.Timestamp > cmdSlice[j].Cmd.Timestamp
	})
	cmdData := map[string]CmdInfo{}
	// according to query
	if query.Count > 0 {
		for i := 0; i < len(cmdSlice); i++ {
			if query.Filter != "" && !strings.Contains(cmdSlice[i].Cmd.Command, query.Filter) {
				continue
			}

			if query.Duration != "" {
				duration, err := time.ParseDuration(query.Duration)
				if err != nil {
					return nil, fmt.Errorf("error: %v", err)
				}
				// parse timestamp string to time.Time
				timestamp, err := time.Parse(time.RFC3339, cmdSlice[i].Cmd.Timestamp)
				if err != nil {
					return nil, fmt.Errorf("error: %v", err)
				}
				if time.Since(timestamp) > duration {
					// sorted by timestamp, so if the first one is not in duration, then all the rest are not in duration
					break
				}
			}

			cmdData[fmt.Sprintf("%d", len(cmdData))] = cmdSlice[i].Cmd
			if len(cmdData) >= query.Count {
				break
			}
		}
	}

	return cmdData, nil
}
