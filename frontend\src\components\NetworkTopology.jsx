import React, { useEffect, useRef, useState } from "react";
import { Card, Button, Space, Select, Tooltip, Badge, Spin } from "antd";
import {
  FullscreenOutlined,
  FullscreenExitOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  ReloadOutlined,
  DownloadOutlined,
} from "@ant-design/icons";
import G6 from "@antv/g6";
import {
  useGetAllGroupsQuery,
  useGetTopologyDataQuery,
} from "../app/services/groupsApi";
import "./NetworkTopology.css";

const { Option } = Select;

const NetworkTopology = () => {
  const graphRef = useRef(null);
  const containerRef = useRef(null);
  const [graph, setGraph] = useState(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [selectedLayout, setSelectedLayout] = useState("compactBox");
  const [loading, setLoading] = useState(true);

  const { data: groups = [], isLoading, error } = useGetAllGroupsQuery();
  const {
    data: topologyData = {},
    isLoading: topologyLoading,
    error: topologyError,
  } = useGetTopologyDataQuery();

  // Node colors for different levels
  const nodeColors = {
    relm: "#1890ff",
    region: "#52c41a",
    zone: "#faad14",
    subnet: "#f5222d",
    subnetGroup: "#722ed1",
    device: "#13c2c2",
    message: "#999999",
  };

  // Convert groups data to G6 tree format
  const convertToTreeData = (groupsData, topologyData) => {
    // If no group data, create a simple topology from device connections
    if (!groupsData || groupsData.length === 0) {
      if (!topologyData || Object.keys(topologyData).length === 0) {
        return {
          id: "root",
          label: "No Network Data Available",
          type: "root",
          children: [
            {
              id: "empty-message",
              label: "No groups or devices found",
              type: "message",
              children: [],
            },
          ],
        };
      }

      // Create topology from device connections only
      const treeData = {
        id: "root",
        label: "Device Topology",
        type: "root",
        children: [],
      };

      // Add devices from topology data
      Object.values(topologyData).forEach((device) => {
        if (device.Id && device.Id !== "11-22-33-44-55-66") {
          const deviceNode = {
            id: `device-${device.Id}`,
            label: device.Id,
            type: "device",
            data: {
              mac: device.Id,
              ipAddress: device.IpAddress,
              modelName: device.ModelName,
              services: device.Services,
              links: device.LinkData || [],
            },
            children: [],
          };
          treeData.children.push(deviceNode);
        }
      });

      return treeData;
    }

    // Normal hierarchical topology with groups
    const treeData = {
      id: "root",
      label: "Network Topology",
      type: "root",
      children: [],
    };

    groupsData.forEach((relm) => {
      const relmNode = {
        id: `relm-${relm.name}`,
        label: relm.name,
        type: "relm",
        data: relm,
        children: [],
      };

      relm.regions?.forEach((region) => {
        const regionNode = {
          id: `region-${relm.name}-${region.name}`,
          label: region.name,
          type: "region",
          data: region,
          children: [],
        };

        region.zones?.forEach((zone) => {
          const zoneNode = {
            id: `zone-${relm.name}-${region.name}-${zone.name}`,
            label: zone.name,
            type: "zone",
            data: zone,
            children: [],
          };

          zone.subnets?.forEach((subnet) => {
            const subnetNode = {
              id: `subnet-${relm.name}-${region.name}-${zone.name}-${subnet.name}`,
              label: subnet.name,
              type: "subnet",
              data: subnet,
              children: [],
            };

            subnet.subnetGroups?.forEach((subnetGroup) => {
              const subnetGroupNode = {
                id: `subnetgroup-${relm.name}-${region.name}-${zone.name}-${subnet.name}-${subnetGroup.name}`,
                label: subnetGroup.name,
                type: "subnetGroup",
                data: subnetGroup,
                children: [],
              };

              // Add devices as leaf nodes with topology connection data
              subnetGroup.devices?.forEach((deviceMac) => {
                const deviceTopologyData = topologyData[deviceMac] || {};
                subnetGroupNode.children.push({
                  id: `device-${subnetGroup.name}-${deviceMac}`,
                  label: deviceMac,
                  type: "device",
                  data: {
                    mac: deviceMac,
                    ipAddress: deviceTopologyData.IpAddress,
                    modelName: deviceTopologyData.ModelName,
                    services: deviceTopologyData.Services,
                    links: deviceTopologyData.LinkData || [],
                    lastUpdated: deviceTopologyData.LastUpdated,
                  },
                });
              });

              subnetNode.children.push(subnetGroupNode);
            });

            zoneNode.children.push(subnetNode);
          });

          regionNode.children.push(zoneNode);
        });

        relmNode.children.push(regionNode);
      });

      treeData.children.push(relmNode);
    });

    return treeData;
  };

  // Register custom nodes
  const registerCustomNodes = () => {
    // Register rounded rectangle node
    G6.registerNode("rounded-rect", {
      draw(cfg, group) {
        const { label, type, data } = cfg;
        const color = nodeColors[type] || "#666";

        // Calculate text width for dynamic sizing
        const textWidth = G6.Util.getTextSize(label, 12)[0];
        const width = Math.max(textWidth + 30, 100);
        const height = type === "device" ? 25 : 35;

        // Main rectangle with gradient
        const rect = group.addShape("rect", {
          attrs: {
            x: -width / 2,
            y: -height / 2,
            width,
            height,
            radius: type === "device" ? 4 : 8,
            fill: `l(90) 0:${color} 1:${G6.Util.mix(color, "#fff", 0.2)}`,
            stroke: type === "root" ? "#000" : "#fff",
            strokeWidth: type === "root" ? 3 : 2,
            shadowColor: "rgba(0,0,0,0.15)",
            shadowBlur: 6,
            shadowOffsetY: 3,
          },
          name: "main-rect",
        });

        // Label text
        group.addShape("text", {
          attrs: {
            x: 0,
            y: type === "device" ? 0 : -2,
            text: label,
            fontSize: type === "device" ? 10 : type === "root" ? 14 : 12,
            fontWeight:
              type === "root" ? "bold" : type === "device" ? "normal" : "bold",
            fill: "#fff",
            textAlign: "center",
            textBaseline: "middle",
          },
          name: "label-text",
        });

        // Device count badge for non-device nodes
        if (type !== "device" && type !== "root" && data) {
          let deviceCount = 0;
          if (type === "subnetGroup") {
            deviceCount = data.devices?.length || 0;
          } else if (type === "subnet") {
            deviceCount =
              data.subnetGroups?.reduce(
                (sum, sg) => sum + (sg.devices?.length || 0),
                0
              ) || 0;
          } else if (type === "zone") {
            deviceCount =
              data.subnets?.reduce(
                (sum, subnet) =>
                  sum +
                  (subnet.subnetGroups?.reduce(
                    (sgSum, sg) => sgSum + (sg.devices?.length || 0),
                    0
                  ) || 0),
                0
              ) || 0;
          }

          if (deviceCount > 0) {
            // Badge background
            group.addShape("circle", {
              attrs: {
                x: width / 2 - 8,
                y: -height / 2 + 8,
                r: 8,
                fill: "#ff4d4f",
                stroke: "#fff",
                strokeWidth: 1,
              },
              name: "badge-bg",
            });

            // Badge text
            group.addShape("text", {
              attrs: {
                x: width / 2 - 8,
                y: -height / 2 + 8,
                text: deviceCount.toString(),
                fontSize: 9,
                fill: "#fff",
                textAlign: "center",
                textBaseline: "middle",
                fontWeight: "bold",
              },
              name: "badge-text",
            });
          }
        }

        // Type indicator for non-root nodes
        if (type !== "root") {
          group.addShape("text", {
            attrs: {
              x: -width / 2 + 8,
              y: -height / 2 + 8,
              text: type.charAt(0).toUpperCase(),
              fontSize: 8,
              fill: "rgba(255,255,255,0.8)",
              textAlign: "center",
              textBaseline: "middle",
              fontWeight: "bold",
            },
            name: "type-indicator",
          });
        }

        return rect;
      },

      update(cfg, item) {
        const group = item.getContainer();
        const { label, type } = cfg;
        const color = nodeColors[type] || "#666";

        const rect = group.find((e) => e.get("name") === "main-rect");
        const text = group.find((e) => e.get("name") === "label-text");

        if (rect) {
          rect.attr(
            "fill",
            `l(90) 0:${color} 1:${G6.Util.mix(color, "#fff", 0.2)}`
          );
        }
        if (text) {
          text.attr("text", label);
        }
      },

      setState(name, value, item) {
        const group = item.getContainer();
        const rect = group.find((e) => e.get("name") === "main-rect");

        if (name === "selected") {
          if (value) {
            rect.attr("strokeWidth", 4);
            rect.attr("stroke", "#faad14");
          } else {
            rect.attr("strokeWidth", 2);
            rect.attr("stroke", "#fff");
          }
        } else if (name === "hover") {
          if (value) {
            rect.attr("shadowBlur", 10);
            rect.attr("shadowOffsetY", 5);
          } else {
            rect.attr("shadowBlur", 6);
            rect.attr("shadowOffsetY", 3);
          }
        }
      },
    });
  };

  // Initialize graph
  const initGraph = () => {
    if (!containerRef.current) return;

    registerCustomNodes();

    const width = containerRef.current.offsetWidth;
    const height = containerRef.current.offsetHeight;

    const newGraph = new G6.TreeGraph({
      container: containerRef.current,
      width,
      height,
      modes: {
        default: ["drag-canvas", "zoom-canvas", "drag-node"],
      },
      defaultNode: {
        type: "rounded-rect",
        size: [80, 30],
        style: {
          stroke: "#fff",
          strokeWidth: 2,
        },
      },
      defaultEdge: {
        type: "cubic-vertical",
        style: {
          stroke: "#A3B1BF",
          strokeWidth: 2,
          endArrow: {
            path: G6.Arrow.triangle(8, 8, 0),
            fill: "#A3B1BF",
          },
        },
      },
      layout: {
        type: selectedLayout,
        direction: "TB",
        getId: (d) => d.id,
        getHeight: () => 60,
        getWidth: () => 120,
        getVGap: () => 40,
        getHGap: () => 60,
      },
      animate: true,
      animateCfg: {
        duration: 500,
        easing: "easeCubic",
      },
    });

    // Add event listeners
    newGraph.on("node:click", (e) => {
      const { item } = e;
      const model = item.getModel();
      console.log("Node clicked:", model);

      // Highlight clicked node
      newGraph.getNodes().forEach((node) => {
        newGraph.clearItemStates(node);
      });
      newGraph.setItemState(item, "selected", true);

      // Show node details in console or could be expanded to show in a modal
      if (model.data) {
        console.log("Node details:", {
          type: model.type,
          label: model.label,
          data: model.data,
        });
      }
    });

    newGraph.on("node:mouseenter", (e) => {
      const { item } = e;
      const model = item.getModel();
      newGraph.setItemState(item, "hover", true);

      // Show tooltip with node information
      const { type, label, data } = model;
      let tooltipContent = `<strong>${label}</strong><br/>Type: ${type}`;

      if (type === "device" && data) {
        if (data.ipAddress) tooltipContent += `<br/>IP: ${data.ipAddress}`;
        if (data.modelName) tooltipContent += `<br/>Model: ${data.modelName}`;
        if (data.services) tooltipContent += `<br/>Service: ${data.services}`;
        if (data.links && data.links.length > 0) {
          tooltipContent += `<br/>Connections: ${data.links.length}`;
        }
      } else if (type === "subnetGroup" && data?.devices) {
        tooltipContent += `<br/>Devices: ${data.devices.length}`;
      } else if (type === "subnet" && data?.subnetGroups) {
        const deviceCount = data.subnetGroups.reduce(
          (sum, sg) => sum + (sg.devices?.length || 0),
          0
        );
        tooltipContent += `<br/>SubnetGroups: ${data.subnetGroups.length}<br/>Total Devices: ${deviceCount}`;
      }

      // Set cursor to pointer
      item.getContainer().attr("cursor", "pointer");
    });

    newGraph.on("node:mouseleave", (e) => {
      const { item } = e;
      newGraph.setItemState(item, "hover", false);
      item.getContainer().attr("cursor", "default");
    });

    setGraph(newGraph);
    return newGraph;
  };

  // Update graph data
  const updateGraphData = () => {
    if (!graph) return;

    const treeData = convertToTreeData(groups, topologyData);
    if (treeData) {
      graph.data(treeData);
      graph.render();
      graph.fitView();
      setLoading(false);
    }
  };

  // Layout change handler
  const handleLayoutChange = (layout) => {
    setSelectedLayout(layout);
    if (graph) {
      graph.updateLayout({
        type: layout,
        direction: "TB",
        getId: (d) => d.id,
        getHeight: () => 60,
        getWidth: () => 120,
        getVGap: () => 40,
        getHGap: () => 60,
      });
    }
  };

  // Control handlers
  const handleZoomIn = () => graph?.zoomTo(graph.getZoom() * 1.2);
  const handleZoomOut = () => graph?.zoomTo(graph.getZoom() * 0.8);
  const handleFitView = () => graph?.fitView();
  const handleRefresh = () => updateGraphData();

  const handleFullscreen = () => {
    if (!isFullscreen) {
      containerRef.current?.requestFullscreen?.();
    } else {
      document.exitFullscreen?.();
    }
    setIsFullscreen(!isFullscreen);
  };

  const handleDownload = () => {
    if (graph) {
      graph.downloadFullImage("network-topology", "image/png");
    }
  };

  // Initialize graph on mount
  useEffect(() => {
    const newGraph = initGraph();
    return () => {
      if (newGraph) {
        newGraph.destroy();
      }
    };
  }, [selectedLayout]);

  // Update data when groups or topology data changes
  useEffect(() => {
    if (graph) {
      updateGraphData();
    }
  }, [graph, groups, topologyData]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (graph && containerRef.current) {
        const width = containerRef.current.offsetWidth;
        const height = containerRef.current.offsetHeight;
        graph.changeSize(width, height);
        graph.fitView();
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [graph]);

  if (isLoading || topologyLoading) {
    return (
      <Card title="Network Topology" style={{ height: "100vh" }}>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "60vh",
          }}
        >
          <Spin size="large" />
          <div style={{ marginLeft: "16px" }}>
            Loading {isLoading ? "groups" : "topology"} data...
          </div>
        </div>
      </Card>
    );
  }

  if (error || topologyError) {
    return (
      <Card title="Network Topology" style={{ height: "100vh" }}>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "60vh",
          }}
        >
          <div>
            Error loading data:{" "}
            {error?.message || topologyError?.message || "Unknown error"}
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card
      title="Network Topology"
      style={{ height: "100vh" }}
      extra={
        <Space>
          <Select
            value={selectedLayout}
            onChange={handleLayoutChange}
            style={{ width: 150 }}
          >
            <Option value="compactBox">Compact Box</Option>
            <Option value="dendrogram">Dendrogram</Option>
            <Option value="indented">Indented</Option>
            <Option value="mindmap">Mind Map</Option>
          </Select>

          <Tooltip title="Zoom In">
            <Button icon={<ZoomInOutlined />} onClick={handleZoomIn} />
          </Tooltip>

          <Tooltip title="Zoom Out">
            <Button icon={<ZoomOutOutlined />} onClick={handleZoomOut} />
          </Tooltip>

          <Tooltip title="Fit View">
            <Button icon={<ReloadOutlined />} onClick={handleFitView} />
          </Tooltip>

          <Tooltip title="Refresh">
            <Button icon={<ReloadOutlined />} onClick={handleRefresh} />
          </Tooltip>

          <Tooltip title="Download">
            <Button icon={<DownloadOutlined />} onClick={handleDownload} />
          </Tooltip>

          <Tooltip title={isFullscreen ? "Exit Fullscreen" : "Fullscreen"}>
            <Button
              icon={
                isFullscreen ? (
                  <FullscreenExitOutlined />
                ) : (
                  <FullscreenOutlined />
                )
              }
              onClick={handleFullscreen}
            />
          </Tooltip>
        </Space>
      }
    >
      <div
        ref={containerRef}
        className="network-topology-container"
        style={{
          width: "100%",
          height: "calc(100vh - 120px)",
          position: "relative",
        }}
      >
        {loading && (
          <div className="topology-loading">
            <Spin size="large" />
          </div>
        )}
      </div>

      {/* Legend */}
      <div className="topology-legend fade-in">
        <h4>Legend</h4>
        {Object.entries(nodeColors).map(([type, color]) => (
          <div key={type} className="legend-item">
            <div className="legend-color" style={{ backgroundColor: color }} />
            <span className="legend-label">{type}</span>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default NetworkTopology;
