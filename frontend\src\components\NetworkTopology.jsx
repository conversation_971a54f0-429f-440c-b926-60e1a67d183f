import React, { useEffect, useRef, useState } from "react";
import {
  Card,
  Button,
  Space,
  Select,
  Tooltip,
  Badge,
  Spin,
  Tree,
  Row,
  Col,
  theme as antdTheme,
} from "antd";
import {
  ZoomInOutlined,
  ZoomOutOutlined,
  ReloadOutlined,
  DownloadOutlined,
  ApartmentOutlined,
  GlobalOutlined,
  EnvironmentOutlined,
  ClusterOutlined,
  GroupOutlined,
  LaptopOutlined,
} from "@ant-design/icons";
import G6 from "@antv/g6";
import {
  useGetAllGroupsQuery,
  useGetTopologyDataQuery,
} from "../app/services/groupsApi";
import { TopologyImage } from "./topology/TopologyImage";
import "./NetworkTopology.css";

const { Option } = Select;

// Constants from TopologyPage
const ANIMATION_CONFIG = { duration: 200, easing: "easeCubic" };
const GRAPH_CONFIG = {
  DEFAULT_NODE_SIZE: [56, 56],
  FORCE_LAYOUT: {
    linkDistance: 250,
    nodeStrength: -50,
    edgeStrength: 0.1,
    collideStrength: 0.8,
    nodeSize: 30,
    alpha: 0.3,
    alphaDecay: 0.028,
    alphaMin: 0.01,
  },
  ANIMATION: {
    repeat: true,
    duration: 3000,
  },
};

// G6 Edge Registration (from TopologyPage)
G6.registerEdge(
  "circle-running",
  {
    afterDraw(cfg, group) {
      const shape = group.get("children")[0];
      const startPoint = shape.getPoint(0);
      const color = cfg.circleColor || "#1890ff";

      if (!startPoint || color === "transparent") return;

      // Remove any existing circles to prevent duplicates
      const existingCircles = group.findAll(
        (element) => element.get("name") === "running-circle"
      );
      existingCircles.forEach((circle) => circle.remove());

      const circle = group.addShape("circle", {
        attrs: {
          x: startPoint.x,
          y: startPoint.y,
          fill: color,
          r: 3,
        },
        name: "running-circle",
      });

      circle.animate((ratio) => {
        const tmpPoint = shape.getPoint(ratio);
        return tmpPoint ? { x: tmpPoint.x, y: tmpPoint.y } : { x: 0, y: 0 };
      }, GRAPH_CONFIG.ANIMATION);
    },
    afterUpdate(cfg, item) {
      const group = item.getContainer();
      this.afterDraw(cfg, group);
    },
    setState(name, value, item) {
      if (name === "refresh" && value) {
        const group = item.getContainer();
        const cfg = item.getModel();
        this.afterDraw(cfg, group);
      }
    },
  },
  "line"
);

const NetworkTopology = () => {
  const { token } = antdTheme.useToken();
  const containerRef = useRef(null);
  const [graph, setGraph] = useState(null);
  const [selectedLayout, setSelectedLayout] = useState("force");
  const [loading, setLoading] = useState(true);
  const [selectedSubnetGroup, setSelectedSubnetGroup] = useState(null);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);

  const { data: groups = [], isLoading, error } = useGetAllGroupsQuery();
  const {
    data: topologyData = {},
    isLoading: topologyLoading,
    error: topologyError,
  } = useGetTopologyDataQuery();

  // Convert groups data to tree format for left panel
  const convertToTreeData = (groupsData) => {
    console.log("Converting groups data to tree:", groupsData);
    if (!groupsData || groupsData.length === 0) return [];

    const treeData = [];

    groupsData.forEach((relm) => {
      const relmNode = {
        title: (
          <span>
            <ApartmentOutlined style={{ marginRight: 8, color: "#1890ff" }} />
            {relm.name}
          </span>
        ),
        key: `relm-${relm.name}`,
        type: "relm",
        data: relm,
        children: [],
      };

      relm.regions?.forEach((region) => {
        const regionNode = {
          title: (
            <span>
              <GlobalOutlined style={{ marginRight: 8, color: "#52c41a" }} />
              {region.name}
            </span>
          ),
          key: `region-${relm.name}-${region.name}`,
          type: "region",
          data: region,
          children: [],
        };

        region.zones?.forEach((zone) => {
          const zoneNode = {
            title: (
              <span>
                <EnvironmentOutlined
                  style={{ marginRight: 8, color: "#faad14" }}
                />
                {zone.name}
              </span>
            ),
            key: `zone-${relm.name}-${region.name}-${zone.name}`,
            type: "zone",
            data: zone,
            children: [],
          };

          zone.subnets?.forEach((subnet) => {
            const subnetNode = {
              title: (
                <span>
                  <ClusterOutlined
                    style={{ marginRight: 8, color: "#f5222d" }}
                  />
                  {subnet.name}
                </span>
              ),
              key: `subnet-${relm.name}-${region.name}-${zone.name}-${subnet.name}`,
              type: "subnet",
              data: subnet,
              children: [],
            };

            subnet.subnet_groups?.forEach((subnetGroup) => {
              const deviceCount = subnetGroup.devices?.length || 0;
              console.log(
                `SubnetGroup: ${subnetGroup.name}, devices:`,
                subnetGroup.devices
              );
              const subnetGroupNode = {
                title: (
                  <span>
                    <GroupOutlined
                      style={{ marginRight: 8, color: "#722ed1" }}
                    />
                    {subnetGroup.name}
                    <Badge count={deviceCount} style={{ marginLeft: 8 }} />
                  </span>
                ),
                key: `subnetgroup-${relm.name}-${region.name}-${zone.name}-${subnet.name}-${subnetGroup.name}`,
                type: "subnetGroup",
                data: subnetGroup,
                isLeaf: true,
              };

              subnetNode.children.push(subnetGroupNode);
            });

            zoneNode.children.push(subnetNode);
          });

          regionNode.children.push(zoneNode);
        });

        relmNode.children.push(regionNode);
      });

      treeData.push(relmNode);
    });

    return treeData;
  };

  // Convert topology data to G6 graph format for right panel
  const convertToTopologyGraph = (selectedDevices, topologyData) => {
    const nodes = [];
    const edges = [];

    if (!selectedDevices || selectedDevices.length === 0) {
      nodes.push({
        id: "no-selection",
        label: "Select a SubnetGroup\nto view topology",
        type: "rect",
        style: {
          fill: "#f0f0f0",
          stroke: "#d9d9d9",
        },
        labelCfg: {
          style: {
            fill: token.colorTextSecondary,
            fontSize: 16,
          },
        },
      });
      return { nodes, edges };
    }

    const labelStyle = {
      style: {
        fill: token.colorText,
        fontSize: 12,
        background: {
          fill: "transparent",
          padding: [2, 2, 2, 2],
        },
      },
    };

    // Create nodes for selected devices (following TopologyPage format)
    selectedDevices.forEach((deviceMac) => {
      const deviceTopologyData = topologyData[deviceMac] || {};
      nodes.push({
        id: deviceMac,
        label: `${deviceTopologyData.IpAddress || "N/A"}\n${deviceMac}\n${
          deviceTopologyData.ModelName || "Unknown"
        }`,
        type: "image",
        img: TopologyImage(deviceTopologyData.ModelName),
        size: GRAPH_CONFIG.DEFAULT_NODE_SIZE,
        labelCfg: {
          ...labelStyle,
          position: "bottom",
        },
        originalData: {
          mac: deviceMac,
          ipAddress: deviceTopologyData.IpAddress,
          modelName: deviceTopologyData.ModelName,
          services: deviceTopologyData.Services,
          links: deviceTopologyData.LinkData || [],
        },
      });
    });

    // Create edges between selected devices
    selectedDevices.forEach((deviceMac) => {
      const deviceTopologyData = topologyData[deviceMac] || {};
      if (
        deviceTopologyData.LinkData &&
        deviceTopologyData.LinkData.length > 0
      ) {
        deviceTopologyData.LinkData.forEach((link) => {
          if (
            link.Source &&
            link.Target &&
            link.Source !== link.Target &&
            selectedDevices.includes(link.Target)
          ) {
            edges.push({
              id: `${link.Source}-${link.Target}`,
              source: link.Source,
              target: link.Target,
              label: `${link.Source}_${link.SourcePort}\n${link.Target}_${link.TargetPort}`,
              type: "circle-running",
              color:
                link.BlockedPort === "true"
                  ? "#faad14"
                  : link.LinkType === "manual"
                  ? "#722ed1"
                  : token.colorTextDisabled,
              circleColor:
                link.BlockedPort === "true" ||
                link.LinkType === "dashed" ||
                link.LinkType === "manual"
                  ? "transparent"
                  : token.colorPrimary,
              labelCfg: labelStyle,
              style: {
                lineWidth: 2,
                lineDash:
                  link.LinkType === "dashed" || link.LinkType === "manual"
                    ? [4, 4]
                    : undefined,
              },
              originalData: {
                sourcePort: link.SourcePort,
                targetPort: link.TargetPort,
                linkType: link.LinkType,
                blockedPort: link.BlockedPort,
              },
            });
          }
        });
      }
    });

    return { nodes, edges };
  };

  // Handle tree selection
  const handleTreeSelect = (selectedKeys, info) => {
    console.log("Tree selection:", {
      selectedKeys,
      nodeType: info.node?.type,
      nodeData: info.node?.data,
    });
    setSelectedKeys(selectedKeys);

    if (info.node && info.node.type === "subnetGroup") {
      const subnetGroupData = info.node.data;
      console.log("Selected SubnetGroup:", subnetGroupData);
      setSelectedSubnetGroup(subnetGroupData);

      // Update topology graph with selected devices
      if (graph && subnetGroupData.devices) {
        console.log("Updating topology with devices:", subnetGroupData.devices);
        updateTopologyGraph(subnetGroupData.devices);
      }
    } else {
      console.log("Non-subnetGroup selected, clearing topology");
      setSelectedSubnetGroup(null);
      if (graph) {
        updateTopologyGraph([]);
      }
    }
  };

  // Auto-expand all tree nodes
  useEffect(() => {
    if (groups && groups.length > 0) {
      const treeData = convertToTreeData(groups);
      const allKeys = [];

      const extractKeys = (nodes) => {
        nodes.forEach((node) => {
          allKeys.push(node.key);
          if (node.children && node.children.length > 0) {
            extractKeys(node.children);
          }
        });
      };

      extractKeys(treeData);
      setExpandedKeys(allKeys);
    }
  }, [groups]);

  // Initialize graph (based on TopologyPage)
  const initGraph = () => {
    if (!containerRef.current) return;

    const width = containerRef.current.offsetWidth;
    const height = containerRef.current.offsetHeight || 400;

    const newGraph = new G6.Graph({
      container: containerRef.current,
      width,
      height,
      renderer: "svg",
      linkCenter: true,
      animate: true,
      modes: {
        default: ["zoom-canvas", "drag-canvas", "click-select", "drag-node"],
      },
      defaultNode: {
        type: "image",
        size: GRAPH_CONFIG.DEFAULT_NODE_SIZE,
      },
      defaultEdge: {
        type: "circle-running",
      },
      layout: {
        type: "force",
        ...GRAPH_CONFIG.FORCE_LAYOUT,
      },
    });

    setGraph(newGraph);
    return newGraph;
  };

  // Update topology graph with selected devices
  const updateTopologyGraph = (selectedDevices) => {
    if (!graph) return;

    const graphData = convertToTopologyGraph(selectedDevices, topologyData);
    if (graphData && graphData.nodes) {
      graph.data(graphData);
      graph.render();
      graph.fitView();

      // Force refresh edge animations after render (from TopologyPage)
      setTimeout(() => {
        graph.getEdges().forEach((edge) => {
          edge.setState("refresh", true);
          edge.setState("refresh", false);
        });
      }, 100);
    }
  };

  // Control handlers
  const handleZoomIn = () => graph?.zoomTo(graph.getZoom() * 1.2);
  const handleZoomOut = () => graph?.zoomTo(graph.getZoom() * 0.8);
  const handleFitView = () => graph?.fitView();
  const handleDownload = () => {
    if (graph) {
      graph.downloadFullImage("network-topology", "image/png");
    }
  };

  // Initialize graph on mount
  useEffect(() => {
    if (containerRef.current && !graph && !isLoading && !topologyLoading) {
      console.log("Initializing graph...");
      const newGraph = initGraph();
      if (newGraph) {
        const graphData = convertToTopologyGraph([], topologyData);
        newGraph.data(graphData);
        newGraph.render();
        setLoading(false);
        console.log("Graph initialized successfully");
      }
    }
  }, [graph, isLoading, topologyLoading, topologyData]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (graph) {
        graph.destroy();
      }
    };
  }, []);

  // Update topology when data changes
  useEffect(() => {
    if (graph && selectedSubnetGroup) {
      updateTopologyGraph(selectedSubnetGroup.devices || []);
    }
  }, [topologyData]);

  // Timeout mechanism to prevent infinite loading
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (loading) {
        console.log("Forcing loading to false after timeout");
        setLoading(false);
      }
    }, 5000); // 5 second timeout

    return () => clearTimeout(timeout);
  }, [loading]);

  if (isLoading && topologyLoading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
        }}
      >
        <Spin size="large" />
        <span style={{ marginLeft: 16 }}>Loading network data...</span>
      </div>
    );
  }

  return (
    <div style={{ height: "100vh", padding: "16px" }}>
      <Row gutter={16} style={{ height: "100%" }}>
        {/* Left Panel - Group Hierarchy Tree */}
        <Col span={8}>
          <Card
            title={
              <span>
                <ApartmentOutlined style={{ marginRight: 8 }} />
                Group Hierarchy
              </span>
            }
            style={{ height: "100%" }}
            styles={{ body: { height: "calc(100% - 57px)", overflow: "auto" } }}
          >
            {groups && groups.length > 0 ? (
              <Tree
                showIcon
                defaultExpandAll
                expandedKeys={expandedKeys}
                selectedKeys={selectedKeys}
                onSelect={handleTreeSelect}
                treeData={convertToTreeData(groups)}
                style={{ fontSize: "14px" }}
              />
            ) : (
              <div
                style={{ textAlign: "center", padding: "20px", color: "#999" }}
              >
                <ApartmentOutlined
                  style={{ fontSize: "48px", marginBottom: "16px" }}
                />
                <div>No group hierarchy available</div>
              </div>
            )}
          </Card>
        </Col>

        {/* Right Panel - Device Topology */}
        <Col span={16}>
          <Card
            title={
              <span>
                <LaptopOutlined style={{ marginRight: 8 }} />
                Device Topology
                {selectedSubnetGroup && (
                  <span
                    style={{
                      marginLeft: 16,
                      color: "#722ed1",
                      fontSize: "14px",
                    }}
                  >
                    ({selectedSubnetGroup.name} -{" "}
                    {selectedSubnetGroup.devices?.length || 0} devices)
                  </span>
                )}
              </span>
            }
            style={{ height: "100%" }}
            extra={
              <Space>
                <Tooltip title="Zoom In">
                  <Button
                    size="small"
                    icon={<ZoomInOutlined />}
                    onClick={handleZoomIn}
                  />
                </Tooltip>

                <Tooltip title="Zoom Out">
                  <Button
                    size="small"
                    icon={<ZoomOutOutlined />}
                    onClick={handleZoomOut}
                  />
                </Tooltip>

                <Tooltip title="Fit View">
                  <Button
                    size="small"
                    icon={<ReloadOutlined />}
                    onClick={handleFitView}
                  />
                </Tooltip>

                <Tooltip title="Download">
                  <Button
                    size="small"
                    icon={<DownloadOutlined />}
                    onClick={handleDownload}
                  />
                </Tooltip>
              </Space>
            }
          >
            <div
              ref={containerRef}
              style={{
                width: "100%",
                height: "calc(100% - 57px)",
                minHeight: "400px",
                border: "1px solid #d9d9d9",
                borderRadius: "6px",
                position: "relative",
                backgroundColor: "#fafafa",
              }}
            >
              {loading && (
                <div
                  style={{
                    position: "absolute",
                    top: "50%",
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                    zIndex: 1000,
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    gap: "12px",
                  }}
                >
                  <Spin size="large" />
                  <span style={{ color: "#666", fontSize: "14px" }}>
                    Loading topology...
                  </span>
                </div>
              )}
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default NetworkTopology;
