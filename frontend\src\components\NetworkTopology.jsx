import React, { useEffect, useRef, useState } from "react";
import {
  Card,
  Button,
  Space,
  Select,
  Toolt<PERSON>,
  Badge,
  Spin,
  Tree,
  Row,
  Col,
  theme as antdTheme,
  message,
  Popconfirm,
  Flex,
} from "antd";
import {
  ZoomInOutlined,
  ZoomOutOutlined,
  DownloadOutlined,
  DeleteOutlined,
  ApartmentOutlined,
  GlobalOutlined,
  EnvironmentOutlined,
  ClusterOutlined,
  GroupOutlined,
  LaptopOutlined,
  Pic<PERSON>enterOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import G6 from "@antv/g6";
import {
  useGetNetworkTopologyDataQuery,
  useCleanupGroupsMutation,
} from "../app/services/groupsApi";
import { TopologyImage } from "./topology/TopologyImage";

const { Option } = Select;

// Constants from TopologyPage
const ANIMATION_CONFIG = { duration: 200, easing: "easeCubic" };
const GRAPH_CONFIG = {
  DEFAULT_NODE_SIZE: [56, 56],
  FORCE_LAYOUT: {
    linkDistance: 250,
    nodeStrength: -50,
    edgeStrength: 0.1,
    collideStrength: 0.8,
    nodeSize: 30,
    alpha: 0.3,
    alphaDecay: 0.028,
    alphaMin: 0.01,
  },
  ANIMATION: {
    repeat: true,
    duration: 3000,
  },
};

// G6 Edge Registration (from TopologyPage)
G6.registerEdge(
  "circle-running",
  {
    afterDraw(cfg, group) {
      const shape = group.get("children")[0];
      const startPoint = shape.getPoint(0);
      const color = cfg.circleColor || "#1890ff";

      if (!startPoint || color === "transparent") return;

      // Remove any existing circles to prevent duplicates
      const existingCircles = group.findAll(
        (element) => element.get("name") === "running-circle"
      );
      existingCircles.forEach((circle) => circle.remove());

      const circle = group.addShape("circle", {
        attrs: {
          x: startPoint.x,
          y: startPoint.y,
          fill: color,
          r: 3,
        },
        name: "running-circle",
      });

      circle.animate((ratio) => {
        const tmpPoint = shape.getPoint(ratio);
        return tmpPoint ? { x: tmpPoint.x, y: tmpPoint.y } : { x: 0, y: 0 };
      }, GRAPH_CONFIG.ANIMATION);
    },
    afterUpdate(cfg, item) {
      const group = item.getContainer();
      this.afterDraw(cfg, group);
    },
    setState(name, value, item) {
      if (name === "refresh" && value) {
        const group = item.getContainer();
        const cfg = item.getModel();
        this.afterDraw(cfg, group);
      }
    },
  },
  "line"
);

const NetworkTopology = () => {
  const { token } = antdTheme.useToken();
  const containerRef = useRef(null);
  const [graph, setGraph] = useState(null);
  const [treeGraph, setTreeGraph] = useState(null);
  const [selectedLayout, setSelectedLayout] = useState("force");
  const [loading, setLoading] = useState(true);
  const [selectedSubnetGroup, setSelectedSubnetGroup] = useState(null);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);

  const {
    data: networkData,
    isLoading,
    error,
    refetch,
  } = useGetNetworkTopologyDataQuery();

  const [cleanupGroups] = useCleanupGroupsMutation();

  // Extract data from the combined response
  const groups = networkData?.groups || [];
  const topologyData = networkData?.topologyData || {};
  const subnetGroups = networkData?.subnetGroups || [];

  // Debug logging
  useEffect(() => {
    if (networkData) {
      console.log("Network data received:", networkData);
      console.log("Groups:", groups);
      console.log("Topology data keys:", Object.keys(topologyData));
      console.log("Subnet groups:", subnetGroups);
    }
  }, [networkData]);

  // Convert groups data to tree format for left panel
  const convertToTreeData = (groupsData) => {
    console.log("Converting groups data to tree:", groupsData);
    if (!groupsData || groupsData.length === 0) return [];

    const treeData = [];

    groupsData.forEach((relm) => {
      const relmNode = {
        title: (
          <span>
            <ApartmentOutlined style={{ marginRight: 8, color: "#1890ff" }} />
            {relm.name}
          </span>
        ),
        key: `relm-${relm.name}`,
        type: "relm",
        data: relm,
        children: [],
      };

      relm.regions?.forEach((region) => {
        const regionNode = {
          title: (
            <span>
              <GlobalOutlined style={{ marginRight: 8, color: "#52c41a" }} />
              {region.name}
            </span>
          ),
          key: `region-${relm.name}-${region.name}`,
          type: "region",
          data: region,
          children: [],
        };

        region.zones?.forEach((zone) => {
          const zoneNode = {
            title: (
              <span>
                <EnvironmentOutlined
                  style={{ marginRight: 8, color: "#faad14" }}
                />
                {zone.name}
              </span>
            ),
            key: `zone-${relm.name}-${region.name}-${zone.name}`,
            type: "zone",
            data: zone,
            children: [],
          };

          zone.subnets?.forEach((subnet) => {
            const subnetNode = {
              title: (
                <span>
                  <ClusterOutlined
                    style={{ marginRight: 8, color: "#f5222d" }}
                  />
                  {subnet.name}
                </span>
              ),
              key: `subnet-${relm.name}-${region.name}-${zone.name}-${subnet.name}`,
              type: "subnet",
              data: subnet,
              children: [],
            };

            subnet.subnet_groups?.forEach((subnetGroup) => {
              const deviceCount = subnetGroup.devices?.length || 0;
              console.log(
                `SubnetGroup: ${subnetGroup.name}, devices:`,
                subnetGroup.devices
              );
              const subnetGroupNode = {
                title: (
                  <span>
                    <GroupOutlined
                      style={{ marginRight: 8, color: "#722ed1" }}
                    />
                    {subnetGroup.name}
                    <Badge count={deviceCount} style={{ marginLeft: 8 }} />
                  </span>
                ),
                key: `subnetgroup-${relm.name}-${region.name}-${zone.name}-${subnet.name}-${subnetGroup.name}`,
                type: "subnetGroup",
                data: subnetGroup,
                isLeaf: true,
              };

              subnetNode.children.push(subnetGroupNode);
            });

            zoneNode.children.push(subnetNode);
          });

          regionNode.children.push(zoneNode);
        });

        relmNode.children.push(regionNode);
      });

      treeData.push(relmNode);
    });

    return treeData;
  };

  // Convert topology data to G6 graph format for right panel
  const convertToTopologyGraph = (selectedDevices, topologyData) => {
    const nodes = [];
    const edges = [];

    if (!selectedDevices || selectedDevices.length === 0) {
      return { nodes, edges };
    }

    const labelStyle = {
      style: {
        fill: token.colorText,
        fontSize: 12,
        background: {
          fill: "transparent",
          padding: [2, 2, 2, 2],
        },
      },
    };

    // Create nodes for selected devices (following TopologyPage format)
    selectedDevices.forEach((deviceMac) => {
      const deviceTopologyData = topologyData[deviceMac] || {};
      console.log(
        `Regular topology device ${deviceMac} data:`,
        deviceTopologyData
      );

      nodes.push({
        id: deviceMac,
        label: `${
          deviceTopologyData.ipAddress || deviceTopologyData.IpAddress || "N/A"
        }\n${deviceMac}\n${
          deviceTopologyData.modelname ||
          deviceTopologyData.ModelName ||
          "Unknown"
        }`,
        type: "image",
        img: TopologyImage(
          deviceTopologyData.modelname || deviceTopologyData.ModelName
        ),
        size: GRAPH_CONFIG.DEFAULT_NODE_SIZE,
        labelCfg: {
          ...labelStyle,
          position: "bottom",
        },
        originalData: {
          mac: deviceMac,
          ipAddress:
            deviceTopologyData.ipAddress || deviceTopologyData.IpAddress,
          modelName:
            deviceTopologyData.modelname || deviceTopologyData.ModelName,
          services: deviceTopologyData.services || deviceTopologyData.Services,
          links:
            deviceTopologyData.linkData || deviceTopologyData.LinkData || [],
        },
      });
    });

    // Create edges between selected devices with duplicate handling
    const edgeMap = new Map(); // To track unique edges and prioritize blocked ports

    selectedDevices.forEach((deviceMac) => {
      const deviceTopologyData = topologyData[deviceMac] || {};
      const linkData =
        deviceTopologyData.linkData || deviceTopologyData.LinkData || [];

      if (linkData && linkData.length > 0) {
        linkData.forEach((link) => {
          const source = link.source || link.Source;
          const target = link.target || link.Target;
          const sourcePort = link.sourcePort || link.SourcePort;
          const targetPort = link.targetPort || link.TargetPort;
          const linkType = link.linkType || link.LinkType;
          const blockedPort = link.blockedPort || link.BlockedPort;

          if (
            source &&
            target &&
            source !== target &&
            selectedDevices.includes(target)
          ) {
            // Create unique edge key (bidirectional)
            const edgeKey =
              source < target ? `${source}-${target}` : `${target}-${source}`;

            const edgeData = {
              id: `${source}-${target}`,
              source: source,
              target: target,
              label: `${source}_${sourcePort}\n${target}_${targetPort}`,
              type: "circle-running",
              color:
                blockedPort === "true"
                  ? "#faad14"
                  : linkType === "manual"
                  ? "#722ed1"
                  : token.colorTextDisabled,
              circleColor:
                blockedPort === "true" ||
                linkType === "dashed" ||
                linkType === "manual"
                  ? "transparent"
                  : token.colorPrimary,
              labelCfg: labelStyle,
              style: {
                lineWidth: 2,
                lineDash:
                  linkType === "dashed" || linkType === "manual"
                    ? [4, 4]
                    : undefined,
              },
              originalData: {
                sourcePort: sourcePort,
                targetPort: targetPort,
                linkType: linkType,
                blockedPort: blockedPort,
              },
              priority: blockedPort === "true" ? 1 : 0, // Higher priority for blocked ports
            };

            // Check if edge already exists
            if (edgeMap.has(edgeKey)) {
              const existingEdge = edgeMap.get(edgeKey);
              // Replace if current edge has higher priority (blocked port)
              if (edgeData.priority > existingEdge.priority) {
                console.log(
                  `Replacing edge ${edgeKey} with higher priority (blocked port)`
                );
                edgeMap.set(edgeKey, edgeData);
              }
            } else {
              console.log(`Creating regular edge: ${source} -> ${target}`);
              edgeMap.set(edgeKey, edgeData);
            }
          }
        });
      }
    });

    // Convert map to array and remove priority field
    edgeMap.forEach((edgeData) => {
      delete edgeData.priority;
      edges.push(edgeData);
    });

    return { nodes, edges };
  };

  // Create hierarchical tree structure based on selected level
  const createHierarchicalTree = (selectedNode, nodeType) => {
    console.log("createHierarchicalTree called with:", {
      selectedNode,
      nodeType,
    });

    const labelStyle = {
      style: {
        fill: token.colorText,
        fontSize: 12,
        background: {
          fill: "transparent",
          padding: [2, 2, 2, 2],
        },
      },
    };

    const createTreeNode = (data, type, color) => {
      let deviceCount = 0;

      // Calculate total device count recursively
      const countDevices = (item) => {
        if (type === "subnetGroup") {
          return item.devices?.length || 0;
        }

        let count = 0;
        if (item.subnet_groups) {
          item.subnet_groups.forEach(
            (sg) => (count += sg.devices?.length || 0)
          );
        }
        if (item.subnets) {
          item.subnets.forEach((subnet) => (count += countDevices(subnet)));
        }
        if (item.zones) {
          item.zones.forEach((zone) => (count += countDevices(zone)));
        }
        if (item.regions) {
          item.regions.forEach((region) => (count += countDevices(region)));
        }
        return count;
      };

      deviceCount = countDevices(data);

      return {
        id: `${type}-${data.name}`,
        label: `${data.name}\n(${deviceCount} devices)`,
        type: "rect",
        style: {
          fill: color,
          stroke: "#d9d9d9",
          radius: 4,
        },
        labelCfg: {
          ...labelStyle,
          position: "center",
          style: {
            ...labelStyle.style,
            fill: "#fff",
            fontWeight: "bold",
          },
        },
        originalData: data,
        children: [],
      };
    };

    const buildHierarchicalTree = (data, currentType) => {
      console.log(`Building tree for ${currentType}:`, data);

      switch (currentType) {
        case "relm":
          console.log("Building relm node, regions:", data.regions);
          const relmNode = createTreeNode(data, "relm", "#1890ff");

          data.regions?.forEach((region) => {
            console.log("Processing region:", region.name);
            const regionChild = buildHierarchicalTree(region, "region");
            if (regionChild) {
              console.log("Adding region child:", regionChild.id);
              relmNode.children.push(regionChild);
            }
          });

          console.log("Final relm node:", relmNode);
          return relmNode;

        case "region":
          console.log("Building region node, zones:", data.zones);
          const regionNode = createTreeNode(data, "region", "#52c41a");

          data.zones?.forEach((zone) => {
            console.log("Processing zone:", zone.name);
            const zoneChild = buildHierarchicalTree(zone, "zone");
            if (zoneChild) {
              console.log("Adding zone child:", zoneChild.id);
              regionNode.children.push(zoneChild);
            }
          });

          console.log("Final region node:", regionNode);
          return regionNode;

        case "zone":
          console.log("Building zone node, subnets:", data.subnets);
          const zoneNode = createTreeNode(data, "zone", "#faad14");

          data.subnets?.forEach((subnet) => {
            console.log("Processing subnet:", subnet.name);
            const subnetChild = buildHierarchicalTree(subnet, "subnet");
            if (subnetChild) {
              console.log("Adding subnet child:", subnetChild.id);
              zoneNode.children.push(subnetChild);
            }
          });

          console.log("Final zone node:", zoneNode);
          return zoneNode;

        case "subnet":
          console.log(
            "Building subnet node, subnet_groups:",
            data.subnet_groups
          );
          const subnetNode = createTreeNode(data, "subnet", "#f5222d");

          data.subnet_groups?.forEach((subnetGroup) => {
            console.log("Processing subnetGroup:", subnetGroup.name);
            const subnetGroupChild = buildHierarchicalTree(
              subnetGroup,
              "subnetGroup"
            );
            if (subnetGroupChild) {
              console.log("Adding subnetGroup child:", subnetGroupChild.id);
              subnetNode.children.push(subnetGroupChild);
            }
          });

          console.log("Final subnet node:", subnetNode);
          return subnetNode;

        case "subnetGroup":
          console.log("Building subnetGroup node (leaf)");
          const sgNode = createTreeNode(data, "subnetGroup", "#722ed1");
          console.log("Final subnetGroup node:", sgNode);
          return sgNode;

        default:
          console.error("Unknown node type:", currentType);
          return null;
      }
    };

    const result = buildHierarchicalTree(selectedNode, nodeType);
    console.log("Final hierarchical tree result:", result);
    return result;
  };

  // Handle tree selection
  const handleTreeSelect = (selectedKeys, info) => {
    console.log("Tree selection:", {
      selectedKeys,
      nodeType: info.node?.type,
      nodeData: info.node?.data,
    });
    setSelectedKeys(selectedKeys);

    if (info.node) {
      const nodeType = info.node.type;
      const nodeData = info.node.data;

      if (nodeType === "subnetGroup") {
        // Handle SubnetGroup selection - show device topology
        console.log("Selected SubnetGroup:", nodeData);

        // Find the corresponding subnet group topology data from the API response
        const keyParts = info.node.key.split("-");
        const subnetGroupTopology = subnetGroups.find(
          (sg) =>
            sg.name === nodeData.name &&
            sg.relmName === keyParts[1] &&
            sg.regionName === keyParts[2] &&
            sg.zoneName === keyParts[3] &&
            sg.subnetName === keyParts[4]
        );

        if (subnetGroupTopology) {
          console.log("Found subnet group topology:", subnetGroupTopology);
          setSelectedSubnetGroup(subnetGroupTopology);

          // Update topology graph with pre-processed data
          if (graph || treeGraph) {
            console.log(
              "Updating topology with devices:",
              subnetGroupTopology.devices
            );
            updateTopologyGraphWithSubnetGroup(subnetGroupTopology);
          }
        } else {
          console.log("Subnet group topology not found, using basic data");
          setSelectedSubnetGroup(nodeData);
          if (graph || (treeGraph && nodeData.devices)) {
            updateTopologyGraph(nodeData.devices);
          }
        }
      } else {
        // Handle hierarchical level selection - show hierarchical tree
        console.log(`Selected ${nodeType}:`, nodeData);
        console.log("Node data structure:", JSON.stringify(nodeData, null, 2));
        setSelectedSubnetGroup(null);

        if (graph || treeGraph) {
          const hierarchicalTree = createHierarchicalTree(nodeData, nodeType);
          console.log("Created hierarchical tree:", hierarchicalTree);

          if (hierarchicalTree) {
            console.log("Updating graph with hierarchical tree");
            updateTopologyGraphWithHierarchy(hierarchicalTree);
          } else {
            console.error(
              "Failed to create hierarchical tree, showing fallback"
            );
            // Show a simple fallback tree
            const fallbackTree = {
              id: `fallback-${nodeType}`,
              label: `${nodeData.name || "Unknown"}\n(${nodeType})`,
              type: "rect",
              style: {
                fill: "#1890ff",
                stroke: "#d9d9d9",
                radius: 4,
              },
              labelCfg: {
                style: {
                  fill: "#fff",
                  fontSize: 12,
                  fontWeight: "bold",
                },
                position: "center",
              },
              children: [],
            };
            updateTopologyGraphWithHierarchy(fallbackTree);
          }
        }
      }
    } else {
      console.log("No node selected, clearing topology");
      setSelectedSubnetGroup(null);
      if (graph || treeGraph) {
        updateTopologyGraph([]);
      }
    }
  };

  // Auto-expand all tree nodes
  useEffect(() => {
    if (groups && groups.length > 0) {
      const treeData = convertToTreeData(groups);
      const allKeys = [];

      const extractKeys = (nodes) => {
        nodes.forEach((node) => {
          allKeys.push(node.key);
          if (node.children && node.children.length > 0) {
            extractKeys(node.children);
          }
        });
      };

      extractKeys(treeData);
      setExpandedKeys(allKeys);
    }
  }, [groups]);

  // Initialize graph (based on TopologyPage)
  const initGraph = () => {
    if (!containerRef.current) {
      console.error("Container ref not available for graph initialization");
      return null;
    }

    try {
      const width = containerRef.current.offsetWidth || 800;
      const height = containerRef.current.offsetHeight || 400;

      console.log(`Initializing graph with dimensions: ${width}x${height}`);

      const newGraph = new G6.Graph({
        container: containerRef.current,
        width,
        height,
        renderer: "svg",
        linkCenter: true,
        animate: true,
        modes: {
          default: ["zoom-canvas", "drag-canvas", "click-select", "drag-node"],
        },
        defaultNode: {
          type: "image",
          size: GRAPH_CONFIG.DEFAULT_NODE_SIZE,
        },
        defaultEdge: {
          type: "circle-running",
        },
        layout: {
          type: "force",
          ...GRAPH_CONFIG.FORCE_LAYOUT,
        },
      });

      setGraph(newGraph);
      return newGraph;
    } catch (error) {
      console.error("Error initializing graph:", error);
      return null;
    }
  };

  // Initialize G6 TreeGraph for hierarchical topology
  const initTreeGraph = () => {
    if (!containerRef.current) {
      console.error("Container ref not available for TreeGraph initialization");
      return null;
    }

    try {
      const width = containerRef.current.offsetWidth || 800;
      const height = containerRef.current.offsetHeight || 400;

      console.log(`Initializing TreeGraph with dimensions: ${width}x${height}`);

      const newTreeGraph = new G6.TreeGraph({
        container: containerRef.current,
        width,
        height,
        renderer: "svg",
        animate: true,
        modes: {
          default: ["zoom-canvas", "drag-canvas"],
        },
        defaultNode: {
          type: "rect",
          size: [120, 40],
          style: {
            fill: "#1890ff",
            stroke: "#d9d9d9",
            radius: 4,
          },
          labelCfg: {
            style: {
              fill: "#fff",
              fontSize: 12,
              fontWeight: "bold",
            },
            position: "center",
          },
        },
        defaultEdge: {
          type: "cubic-horizontal",
          style: {
            stroke: "#d9d9d9",
            lineWidth: 2,
          },
        },
        layout: {
          type: "compactBox",
          direction: "LR",
          getId: function getId(d) {
            return d.id;
          },
          getHeight: function getHeight() {
            return 40;
          },
          getWidth: function getWidth() {
            return 120;
          },
          getVGap: function getVGap() {
            return 20;
          },
          getHGap: function getHGap() {
            return 100;
          },
        },
      });

      setTreeGraph(newTreeGraph);
      return newTreeGraph;
    } catch (error) {
      console.error("Error initializing TreeGraph:", error);
      return null;
    }
  };

  // Validate graph data before rendering
  const validateGraphData = (graphData) => {
    if (!graphData || typeof graphData !== "object") {
      console.error("Invalid graph data: not an object");
      return false;
    }

    if (!Array.isArray(graphData.nodes)) {
      console.error("Invalid graph data: nodes is not an array");
      return false;
    }

    if (!Array.isArray(graphData.edges)) {
      console.error("Invalid graph data: edges is not an array");
      return false;
    }

    // Validate nodes
    for (let i = 0; i < graphData.nodes.length; i++) {
      const node = graphData.nodes[i];
      if (!node || typeof node !== "object") {
        console.error(`Invalid node at index ${i}: not an object`);
        return false;
      }
      if (!node.id || typeof node.id !== "string") {
        console.error(`Invalid node at index ${i}: missing or invalid id`);
        return false;
      }
    }

    // Validate edges
    for (let i = 0; i < graphData.edges.length; i++) {
      const edge = graphData.edges[i];
      if (!edge || typeof edge !== "object") {
        console.error(`Invalid edge at index ${i}: not an object`);
        return false;
      }
      if (!edge.id || typeof edge.id !== "string") {
        console.error(`Invalid edge at index ${i}: missing or invalid id`);
        return false;
      }
      if (!edge.source || typeof edge.source !== "string") {
        console.error(`Invalid edge at index ${i}: missing or invalid source`);
        return false;
      }
      if (!edge.target || typeof edge.target !== "string") {
        console.error(`Invalid edge at index ${i}: missing or invalid target`);
        return false;
      }
    }

    return true;
  };

  // Update topology graph with selected devices
  const updateTopologyGraph = (selectedDevices) => {
    if (!graph) {
      console.error("Graph not initialized");
      return;
    }

    try {
      const graphData = convertToTopologyGraph(selectedDevices, topologyData);
      console.log("Regular topology update with data:", graphData);

      // Validate data before rendering
      if (!validateGraphData(graphData)) {
        console.error("Graph data validation failed, skipping render");
        return;
      }

      if (graphData && graphData.nodes) {
        // Clear existing data first
        graph.clear();

        // Set new data with error handling
        try {
          graph.data(graphData);

          // Re-render the graph
          graph.render();

          // Force layout update
          graph.updateLayout();

          // Fit view after a short delay
          setTimeout(() => {
            try {
              graph.fitView();

              // Force refresh edge animations after render
              graph.getEdges().forEach((edge) => {
                try {
                  edge.setState("refresh", true);
                  edge.setState("refresh", false);
                } catch (edgeError) {
                  console.error("Error refreshing edge animation:", edgeError);
                }
              });
            } catch (fitViewError) {
              console.error("Error in fitView or edge refresh:", fitViewError);
            }
          }, 200);
        } catch (renderError) {
          console.error("Error rendering graph:", renderError);
          // Try to recover by clearing the graph
          graph.clear();
        }
      }
    } catch (error) {
      console.error("Error in updateTopologyGraph:", error);
    }
  };

  // Update topology graph with pre-processed subnet group data (device topology)
  const updateTopologyGraphWithSubnetGroup = (subnetGroupTopology) => {
    console.log(
      "updateTopologyGraphWithSubnetGroup called with:",
      subnetGroupTopology
    );

    // Clear existing tree graph if it exists
    if (treeGraph) {
      console.log("Clearing existing TreeGraph");
      treeGraph.clear();
      treeGraph.destroy();
      setTreeGraph(null);
    }

    // Initialize regular Graph if not exists or create new one
    let currentGraph = graph;
    if (!currentGraph) {
      console.log("Initializing new Graph for device topology");
      currentGraph = initGraph();
      if (!currentGraph) {
        console.error("Failed to initialize Graph");
        return;
      }
    }

    try {
      const nodes = [];
      const edges = [];

      const labelStyle = {
        style: {
          fill: token.colorText,
          fontSize: 12,
          background: {
            fill: "transparent",
            padding: [2, 2, 2, 2],
          },
        },
      };

      // Create nodes from pre-processed device data
      if (
        subnetGroupTopology.devices &&
        Array.isArray(subnetGroupTopology.devices)
      ) {
        subnetGroupTopology.devices.forEach((deviceMac) => {
          if (!deviceMac || typeof deviceMac !== "string") {
            console.warn("Invalid device MAC:", deviceMac);
            return;
          }

          const deviceData = subnetGroupTopology.deviceData?.[deviceMac] || {};
          console.log(`Device ${deviceMac} data:`, deviceData);

          nodes.push({
            id: deviceMac,
            label: `${
              deviceData.ipAddress || deviceData.IpAddress || "N/A"
            }\n${deviceMac}\n${
              deviceData.modelname || deviceData.ModelName || "Unknown"
            }`,
            type: "image",
            img: TopologyImage(deviceData.modelname || deviceData.ModelName),
            size: GRAPH_CONFIG.DEFAULT_NODE_SIZE,
            labelCfg: {
              ...labelStyle,
              position: "bottom",
            },
            originalData: deviceData,
          });
        });
      }

      // Create edges from pre-processed connections with duplicate handling
      const edgeMap = new Map(); // To track unique edges and prioritize blocked ports

      if (
        subnetGroupTopology.connections &&
        Array.isArray(subnetGroupTopology.connections)
      ) {
        subnetGroupTopology.connections.forEach((connection) => {
          if (!connection || typeof connection !== "object") {
            console.warn("Invalid connection:", connection);
            return;
          }

          if (!connection.source || !connection.target) {
            console.warn("Connection missing source or target:", connection);
            return;
          }

          // Create unique edge key (bidirectional)
          const edgeKey =
            connection.source < connection.target
              ? `${connection.source}-${connection.target}`
              : `${connection.target}-${connection.source}`;

          const edgeData = {
            id: `${connection.source}-${connection.target}`,
            source: connection.source,
            target: connection.target,
            label: `${connection.source}_${connection.sourcePort || "N/A"}\n${
              connection.target
            }_${connection.targetPort || "N/A"}`,
            type: "circle-running",
            color:
              connection.blockedPort === "true"
                ? "#faad14"
                : connection.linkType === "manual"
                ? "#722ed1"
                : token.colorTextDisabled,
            circleColor:
              connection.blockedPort === "true" ||
              connection.linkType === "dashed" ||
              connection.linkType === "manual"
                ? "transparent"
                : token.colorPrimary,
            labelCfg: labelStyle,
            style: {
              lineWidth: 2,
              lineDash:
                connection.linkType === "dashed" ||
                connection.linkType === "manual"
                  ? [4, 4]
                  : undefined,
            },
            originalData: connection,
            priority: connection.blockedPort === "true" ? 1 : 0, // Higher priority for blocked ports
          };

          // Check if edge already exists
          if (edgeMap.has(edgeKey)) {
            const existingEdge = edgeMap.get(edgeKey);
            // Replace if current edge has higher priority (blocked port)
            if (edgeData.priority > existingEdge.priority) {
              console.log(
                `Replacing subnet group edge ${edgeKey} with higher priority (blocked port)`
              );
              edgeMap.set(edgeKey, edgeData);
            }
          } else {
            console.log(
              `Creating subnet group edge: ${connection.source} -> ${connection.target}`
            );
            edgeMap.set(edgeKey, edgeData);
          }
        });
      }

      // Convert map to array and remove priority field
      edgeMap.forEach((edgeData) => {
        delete edgeData.priority;
        edges.push(edgeData);
      });

      const graphData = { nodes, edges };
      console.log("Updating subnet group graph with data:", graphData);

      // Validate data before rendering
      if (!validateGraphData(graphData)) {
        console.error(
          "Subnet group graph data validation failed, skipping render"
        );
        return;
      }

      if (graphData && graphData.nodes && graphData.nodes.length > 0) {
        // Clear existing data first
        currentGraph.clear();

        // Set new data with error handling
        try {
          currentGraph.data(graphData);

          // Re-render the graph
          currentGraph.render();

          // Force layout update
          currentGraph.updateLayout();

          // Fit view after a short delay to ensure layout is complete
          setTimeout(() => {
            try {
              currentGraph.fitView();

              // Force refresh edge animations after render
              currentGraph.getEdges().forEach((edge) => {
                try {
                  edge.setState("refresh", true);
                  edge.setState("refresh", false);
                } catch (edgeError) {
                  console.error("Error refreshing edge animation:", edgeError);
                }
              });
            } catch (fitViewError) {
              console.error("Error in fitView or edge refresh:", fitViewError);
            }
          }, 200);
        } catch (renderError) {
          console.error("Error rendering subnet group graph:", renderError);
          // Try to recover by clearing the graph
          currentGraph.clear();
        }
      } else if (graphData && graphData.nodes && graphData.nodes.length === 0) {
        // Handle empty state
        try {
          currentGraph.clear();
          const emptyData = {
            nodes: [
              {
                id: "no-devices",
                label: "No devices in this SubnetGroup",
                type: "rect",
                style: {
                  fill: "#f0f0f0",
                  stroke: "#d9d9d9",
                },
              },
            ],
            edges: [],
          };

          if (validateGraphData(emptyData)) {
            currentGraph.data(emptyData);
            currentGraph.render();
            currentGraph.fitView();
          }
        } catch (emptyStateError) {
          console.error("Error rendering empty state:", emptyStateError);
        }
      }
    } catch (error) {
      console.error("Error in updateTopologyGraphWithSubnetGroup:", error);
    }
  };

  // Update topology graph with hierarchical tree structure
  const updateTopologyGraphWithHierarchy = (hierarchicalTree) => {
    console.log(
      "updateTopologyGraphWithHierarchy called with:",
      hierarchicalTree
    );

    // Clear existing graph if it exists
    if (graph) {
      console.log("Clearing existing regular graph");
      graph.clear();
      graph.destroy();
      setGraph(null);
    }

    // Initialize TreeGraph if not exists or create new one
    let currentTreeGraph = treeGraph;
    if (!currentTreeGraph) {
      console.log("Initializing new TreeGraph");
      currentTreeGraph = initTreeGraph();
      if (!currentTreeGraph) {
        console.error("Failed to initialize TreeGraph");
        return;
      }
    }

    try {
      console.log(
        "Updating TreeGraph with hierarchical tree:",
        hierarchicalTree
      );

      // Clear existing tree data first
      currentTreeGraph.clear();

      if (hierarchicalTree) {
        // Set tree data with error handling
        try {
          currentTreeGraph.data(hierarchicalTree);

          // Re-render the tree graph
          currentTreeGraph.render();

          // Fit view after a short delay
          setTimeout(() => {
            try {
              currentTreeGraph.fitView();
            } catch (fitViewError) {
              console.error("Error in TreeGraph fitView:", fitViewError);
            }
          }, 200);
        } catch (renderError) {
          console.error("Error rendering hierarchical tree:", renderError);
          // Try to recover by clearing the tree graph
          currentTreeGraph.clear();
        }
      } else {
        // Show empty state
        const emptyTreeData = {
          id: "empty-state",
          label: "No data available",
          children: [],
        };

        try {
          currentTreeGraph.data(emptyTreeData);
          currentTreeGraph.render();
          currentTreeGraph.fitView();
        } catch (emptyStateError) {
          console.error("Error rendering empty state:", emptyStateError);
        }
      }
    } catch (error) {
      console.error("Error in updateTopologyGraphWithHierarchy:", error);
    }
  };

  // Control handlers
  const handleZoomIn = () => graph?.zoomTo(graph.getZoom() * 1.2);
  const handleZoomOut = () => graph?.zoomTo(graph.getZoom() * 0.8);
  const handleFitView = () => {
    if (graph && containerRef.current) {
      // Update graph size to current container dimensions
      const width = containerRef.current.offsetWidth;
      const height = containerRef.current.offsetHeight;

      if (width > 0 && height > 0) {
        graph.changeSize(width, height);
        graph.render();
      }

      graph.fitView();
    }
  };
  const handleDownload = () => {
    if (graph) {
      graph.downloadFullImage("network-topology", "image/png");
    }
  };

  const handleCleanupGroups = async () => {
    try {
      const result = await cleanupGroups().unwrap();
      if (result.hasChanges) {
        message.success(result.message);
      } else {
        message.info(result.message);
      }
    } catch (error) {
      message.error(`Error during cleanup: ${error.data || error.message}`);
    }
  };

  // Initialize graph on mount
  useEffect(() => {
    if (containerRef.current && !graph && !isLoading && networkData) {
      console.log("Initializing graph...");
      try {
        const newGraph = initGraph();
        if (newGraph) {
          const graphData = convertToTopologyGraph([], topologyData);

          // Validate initial data
          if (validateGraphData(graphData)) {
            newGraph.data(graphData);
            newGraph.render();
            setLoading(false);
            console.log("Graph initialized successfully");
          } else {
            console.error("Initial graph data validation failed");
            setLoading(false);
          }
        } else {
          console.error("Failed to initialize graph");
          setLoading(false);
        }
      } catch (error) {
        console.error("Error during graph initialization:", error);
        setLoading(false);
      }
    }
  }, [graph, isLoading, networkData, topologyData]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (graph) {
        graph.destroy();
      }
    };
  }, []);

  // Handle container resize
  useEffect(() => {
    if (!containerRef.current || !graph) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        console.log(`Container resized to: ${width}x${height}`);

        if (width > 0 && height > 0) {
          // Update graph dimensions
          graph.changeSize(width, height);

          // Re-render with current data
          graph.render();

          // Fit view to new dimensions
          setTimeout(() => {
            graph.fitView();
          }, 100);
        }
      }
    });

    resizeObserver.observe(containerRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [graph]);

  // Handle window resize as fallback
  useEffect(() => {
    if (!graph) return;

    const handleWindowResize = () => {
      if (containerRef.current) {
        const width = containerRef.current.offsetWidth;
        const height = containerRef.current.offsetHeight;

        console.log(`Window resize - updating graph to: ${width}x${height}`);

        if (width > 0 && height > 0) {
          graph.changeSize(width, height);
          graph.render();

          setTimeout(() => {
            graph.fitView();
          }, 100);
        }
      }
    };

    window.addEventListener("resize", handleWindowResize);

    return () => {
      window.removeEventListener("resize", handleWindowResize);
    };
  }, [graph]);

  // Update topology when data changes
  useEffect(() => {
    if (graph && selectedSubnetGroup) {
      // Check if selectedSubnetGroup has pre-processed data
      if (selectedSubnetGroup.deviceData && selectedSubnetGroup.connections) {
        updateTopologyGraphWithSubnetGroup(selectedSubnetGroup);
      } else {
        updateTopologyGraph(selectedSubnetGroup.devices || []);
      }
    }
  }, [networkData, selectedSubnetGroup]);

  // Timeout mechanism to prevent infinite loading
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (loading) {
        console.log("Forcing loading to false after timeout");
        setLoading(false);
      }
    }, 5000); // 5 second timeout

    return () => clearTimeout(timeout);
  }, [loading]);

  if (isLoading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
        }}
      >
        <Spin size="large" />
        <span style={{ marginLeft: 16 }}>Loading network data...</span>
      </div>
    );
  }

  return (
    <div>
      <Row gutter={16} style={{ height: "100%" }}>
        {/* Left Panel - Relm Hierarchy Tree */}
        <Col span={7}>
          <Card
            title={
              <span>
                <ApartmentOutlined style={{ marginRight: 8 }} />
                Relm Hierarchy
              </span>
            }
            style={{ height: "100%" }}
            styles={{
              body: {
                height: "calc(100vh - 158px)",
                overflow: "auto",
                padding: 10,
              },
            }}
            extra={
              <Flex gap={10} align="center" wrap="wrap">
                <Popconfirm
                  title="Cleanup Groups"
                  description="Remove devices from subnet groups that no longer exist in the system. This action cannot be undone."
                  onConfirm={handleCleanupGroups}
                  okText="Yes, Cleanup"
                  cancelText="Cancel"
                >
                  <Button
                    icon={<DeleteOutlined />}
                    title="Remove non-existent devices from subnet groups"
                  >
                    Cleanup
                  </Button>
                </Popconfirm>
                <Button icon={<ReloadOutlined />} onClick={refetch} />
              </Flex>
            }
          >
            {groups && groups.length > 0 ? (
              <Tree
                showIcon
                defaultExpandAll
                expandedKeys={expandedKeys}
                selectedKeys={selectedKeys}
                onSelect={handleTreeSelect}
                treeData={convertToTreeData(groups)}
                style={{ fontSize: "14px" }}
              />
            ) : (
              <div
                style={{ textAlign: "center", padding: "20px", color: "#999" }}
              >
                <ApartmentOutlined
                  style={{ fontSize: "48px", marginBottom: "16px" }}
                />
                <div>No relm hierarchy available</div>
              </div>
            )}
          </Card>
        </Col>

        {/* Right Panel - Device Topology */}
        <Col span={17}>
          <Card
            title={
              <span>
                <LaptopOutlined style={{ marginRight: 8 }} />
                Device Topology
                {selectedSubnetGroup && (
                  <span
                    style={{
                      marginLeft: 16,
                      color: "#722ed1",
                      fontSize: "14px",
                    }}
                  >
                    ({selectedSubnetGroup.name} -{" "}
                    {selectedSubnetGroup.devices?.length || 0} devices)
                  </span>
                )}
              </span>
            }
            style={{ height: "100%" }}
            styles={{ body: { padding: 5 } }}
            extra={
              <Space>
                <Tooltip title="Zoom In">
                  <Button
                    size="small"
                    icon={<ZoomInOutlined />}
                    onClick={handleZoomIn}
                  />
                </Tooltip>

                <Tooltip title="Zoom Out">
                  <Button
                    size="small"
                    icon={<ZoomOutOutlined />}
                    onClick={handleZoomOut}
                  />
                </Tooltip>

                <Tooltip title="Fit View">
                  <Button
                    size="small"
                    icon={<PicCenterOutlined />}
                    onClick={handleFitView}
                  />
                </Tooltip>

                <Tooltip title="Download">
                  <Button
                    size="small"
                    icon={<DownloadOutlined />}
                    onClick={handleDownload}
                  />
                </Tooltip>
              </Space>
            }
          >
            <div
              ref={containerRef}
              style={{
                width: "100%",
                height: "calc(100vh - 150px)",
                minHeight: "400px",
                border: "1px solid #d9d9d9",
                borderRadius: "6px",
                position: "relative",
                backgroundColor: "#fafafa",
              }}
            >
              {loading && (
                <div
                  style={{
                    position: "absolute",
                    top: "50%",
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                    zIndex: 1000,
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    gap: "12px",
                  }}
                >
                  <Spin size="large" />
                  <span style={{ color: "#666", fontSize: "14px" }}>
                    Loading topology...
                  </span>
                </div>
              )}
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default NetworkTopology;
