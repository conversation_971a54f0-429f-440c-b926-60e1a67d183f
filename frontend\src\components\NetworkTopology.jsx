import React, { useEffect, useRef, useState } from "react";
import {
  Card,
  Button,
  Space,
  Select,
  Tooltip,
  Badge,
  Spin,
  Tree,
  Row,
  Col,
} from "antd";
import {
  ZoomInOutlined,
  ZoomOutOutlined,
  ReloadOutlined,
  DownloadOutlined,
  ApartmentOutlined,
  GlobalOutlined,
  EnvironmentOutlined,
  ClusterOutlined,
  GroupOutlined,
  LaptopOutlined,
} from "@ant-design/icons";
import G6 from "@antv/g6";
import {
  useGetAllGroupsQuery,
  useGetTopologyDataQuery,
} from "../app/services/groupsApi";
import "./NetworkTopology.css";

const { Option } = Select;

const NetworkTopology = () => {
  const containerRef = useRef(null);
  const [graph, setGraph] = useState(null);
  const [selectedLayout, setSelectedLayout] = useState("force");
  const [loading, setLoading] = useState(true);
  const [selectedSubnetGroup, setSelectedSubnetGroup] = useState(null);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);

  const { data: groups = [], isLoading, error } = useGetAllGroupsQuery();
  const {
    data: topologyData = {},
    isLoading: topologyLoading,
    error: topologyError,
  } = useGetTopologyDataQuery();

  // Convert groups data to tree format for left panel
  const convertToTreeData = (groupsData) => {
    if (!groupsData || groupsData.length === 0) return [];

    const treeData = [];

    groupsData.forEach((relm) => {
      const relmNode = {
        title: (
          <span>
            <ApartmentOutlined style={{ marginRight: 8, color: '#1890ff' }} />
            {relm.name}
          </span>
        ),
        key: `relm-${relm.name}`,
        type: 'relm',
        data: relm,
        children: [],
      };

      relm.regions?.forEach((region) => {
        const regionNode = {
          title: (
            <span>
              <GlobalOutlined style={{ marginRight: 8, color: '#52c41a' }} />
              {region.name}
            </span>
          ),
          key: `region-${relm.name}-${region.name}`,
          type: 'region',
          data: region,
          children: [],
        };

        region.zones?.forEach((zone) => {
          const zoneNode = {
            title: (
              <span>
                <EnvironmentOutlined style={{ marginRight: 8, color: '#faad14' }} />
                {zone.name}
              </span>
            ),
            key: `zone-${relm.name}-${region.name}-${zone.name}`,
            type: 'zone',
            data: zone,
            children: [],
          };

          zone.subnets?.forEach((subnet) => {
            const subnetNode = {
              title: (
                <span>
                  <ClusterOutlined style={{ marginRight: 8, color: '#f5222d' }} />
                  {subnet.name}
                </span>
              ),
              key: `subnet-${relm.name}-${region.name}-${zone.name}-${subnet.name}`,
              type: 'subnet',
              data: subnet,
              children: [],
            };

            subnet.subnetGroups?.forEach((subnetGroup) => {
              const deviceCount = subnetGroup.devices?.length || 0;
              const subnetGroupNode = {
                title: (
                  <span>
                    <GroupOutlined style={{ marginRight: 8, color: '#722ed1' }} />
                    {subnetGroup.name}
                    <Badge count={deviceCount} style={{ marginLeft: 8 }} />
                  </span>
                ),
                key: `subnetgroup-${relm.name}-${region.name}-${zone.name}-${subnet.name}-${subnetGroup.name}`,
                type: 'subnetGroup',
                data: subnetGroup,
                isLeaf: true,
              };

              subnetNode.children.push(subnetGroupNode);
            });

            zoneNode.children.push(subnetNode);
          });

          regionNode.children.push(zoneNode);
        });

        relmNode.children.push(regionNode);
      });

      treeData.push(relmNode);
    });

    return treeData;
  };

  // Convert topology data to G6 graph format for right panel
  const convertToTopologyGraph = (selectedDevices, topologyData) => {
    const nodes = [];
    const edges = [];

    if (!selectedDevices || selectedDevices.length === 0) {
      nodes.push({
        id: 'no-selection',
        label: 'Select a SubnetGroup to view topology',
        type: 'message',
        style: {
          fill: '#f0f0f0',
          stroke: '#d9d9d9',
        },
      });
      return { nodes, edges };
    }

    // Create nodes for selected devices
    selectedDevices.forEach((deviceMac) => {
      const deviceTopologyData = topologyData[deviceMac] || {};
      nodes.push({
        id: deviceMac,
        label: deviceMac,
        type: "device",
        data: {
          mac: deviceMac,
          ipAddress: deviceTopologyData.IpAddress,
          modelName: deviceTopologyData.ModelName,
          services: deviceTopologyData.Services,
          links: deviceTopologyData.LinkData || [],
        },
      });
    });

    // Create edges between selected devices
    selectedDevices.forEach((deviceMac) => {
      const deviceTopologyData = topologyData[deviceMac] || {};
      if (deviceTopologyData.LinkData && deviceTopologyData.LinkData.length > 0) {
        deviceTopologyData.LinkData.forEach((link) => {
          if (
            link.Source &&
            link.Target &&
            link.Source !== link.Target &&
            selectedDevices.includes(link.Target)
          ) {
            edges.push({
              id: `${link.Source}-${link.Target}`,
              source: link.Source,
              target: link.Target,
              label: `${link.SourcePort} → ${link.TargetPort}`,
              style: {
                stroke: link.LinkType === "dashed" ? "#ff4d4f" : "#52c41a",
                lineDash: link.LinkType === "dashed" ? [5, 5] : null,
                strokeWidth: 2,
              },
              data: {
                sourcePort: link.SourcePort,
                targetPort: link.TargetPort,
                linkType: link.LinkType,
                blockedPort: link.BlockedPort,
              },
            });
          }
        });
      }
    });

    return { nodes, edges };
  };

  // Handle tree selection
  const handleTreeSelect = (selectedKeys, info) => {
    setSelectedKeys(selectedKeys);
    
    if (info.node && info.node.type === 'subnetGroup') {
      const subnetGroupData = info.node.data;
      setSelectedSubnetGroup(subnetGroupData);
      
      // Update topology graph with selected devices
      if (graph && subnetGroupData.devices) {
        updateTopologyGraph(subnetGroupData.devices);
      }
    }
  };

  // Auto-expand all tree nodes
  useEffect(() => {
    if (groups && groups.length > 0) {
      const treeData = convertToTreeData(groups);
      const allKeys = [];
      
      const extractKeys = (nodes) => {
        nodes.forEach(node => {
          allKeys.push(node.key);
          if (node.children && node.children.length > 0) {
            extractKeys(node.children);
          }
        });
      };
      
      extractKeys(treeData);
      setExpandedKeys(allKeys);
    }
  }, [groups]);

  // Register custom nodes for topology
  const registerCustomNodes = () => {
    G6.registerNode('rounded-rect', {
      draw(cfg, group) {
        const { label, type } = cfg;
        const color = type === 'device' ? '#13c2c2' : '#999999';
        
        const textWidth = G6.Util.getTextSize(label, 12)[0];
        const width = Math.max(textWidth + 20, 80);
        const height = 30;

        const rect = group.addShape('rect', {
          attrs: {
            x: -width / 2,
            y: -height / 2,
            width,
            height,
            radius: 6,
            fill: color,
            stroke: '#fff',
            strokeWidth: 2,
          },
          name: 'main-rect'
        });

        group.addShape('text', {
          attrs: {
            x: 0,
            y: 0,
            text: label,
            fontSize: 12,
            fontWeight: 'bold',
            fill: '#fff',
            textAlign: 'center',
            textBaseline: 'middle'
          },
          name: 'label-text'
        });

        return rect;
      }
    });
  };

  // Initialize graph
  const initGraph = () => {
    if (!containerRef.current) return;

    registerCustomNodes();

    const width = containerRef.current.offsetWidth;
    const height = containerRef.current.offsetHeight;

    const newGraph = new G6.Graph({
      container: containerRef.current,
      width,
      height,
      modes: {
        default: ["drag-canvas", "zoom-canvas", "drag-node"],
      },
      defaultNode: {
        type: "rounded-rect",
        size: [80, 30],
      },
      defaultEdge: {
        style: {
          stroke: "#A3B1BF",
          strokeWidth: 2,
          endArrow: {
            path: G6.Arrow.triangle(8, 8, 0),
            fill: "#A3B1BF",
          },
        },
      },
      layout: {
        type: "force",
        linkDistance: 200,
        nodeStrength: -300,
        edgeStrength: 0.2,
      },
    });

    setGraph(newGraph);
    return newGraph;
  };

  // Update topology graph with selected devices
  const updateTopologyGraph = (selectedDevices) => {
    if (!graph) return;

    const graphData = convertToTopologyGraph(selectedDevices, topologyData);
    if (graphData && graphData.nodes) {
      graph.data(graphData);
      graph.render();
      graph.fitView();
    }
  };

  // Control handlers
  const handleZoomIn = () => graph?.zoomTo(graph.getZoom() * 1.2);
  const handleZoomOut = () => graph?.zoomTo(graph.getZoom() * 0.8);
  const handleFitView = () => graph?.fitView();
  const handleDownload = () => {
    if (graph) {
      graph.downloadFullImage("network-topology", "image/png");
    }
  };

  // Initialize graph on mount
  useEffect(() => {
    const newGraph = initGraph();
    if (newGraph) {
      const graphData = convertToTopologyGraph([], topologyData);
      newGraph.data(graphData);
      newGraph.render();
      setLoading(false);
    }
    
    return () => {
      if (newGraph) {
        newGraph.destroy();
      }
    };
  }, []);

  // Update topology when data changes
  useEffect(() => {
    if (graph && selectedSubnetGroup) {
      updateTopologyGraph(selectedSubnetGroup.devices || []);
    }
  }, [topologyData]);

  if (isLoading && topologyLoading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" />
        <span style={{ marginLeft: 16 }}>Loading network data...</span>
      </div>
    );
  }

  return (
    <div style={{ height: "100vh", padding: "16px" }}>
      <Row gutter={16} style={{ height: "100%" }}>
        {/* Left Panel - Group Hierarchy Tree */}
        <Col span={8}>
          <Card
            title={
              <span>
                <ApartmentOutlined style={{ marginRight: 8 }} />
                Group Hierarchy
              </span>
            }
            style={{ height: "100%" }}
            bodyStyle={{ height: "calc(100% - 57px)", overflow: "auto" }}
          >
            {groups && groups.length > 0 ? (
              <Tree
                showIcon
                defaultExpandAll
                expandedKeys={expandedKeys}
                selectedKeys={selectedKeys}
                onSelect={handleTreeSelect}
                treeData={convertToTreeData(groups)}
                style={{ fontSize: "14px" }}
              />
            ) : (
              <div style={{ textAlign: "center", padding: "20px", color: "#999" }}>
                <ApartmentOutlined style={{ fontSize: "48px", marginBottom: "16px" }} />
                <div>No group hierarchy available</div>
              </div>
            )}
          </Card>
        </Col>

        {/* Right Panel - Device Topology */}
        <Col span={16}>
          <Card
            title={
              <span>
                <LaptopOutlined style={{ marginRight: 8 }} />
                Device Topology
                {selectedSubnetGroup && (
                  <span style={{ marginLeft: 16, color: "#722ed1", fontSize: "14px" }}>
                    ({selectedSubnetGroup.name} - {selectedSubnetGroup.devices?.length || 0} devices)
                  </span>
                )}
              </span>
            }
            style={{ height: "100%" }}
            extra={
              <Space>
                <Tooltip title="Zoom In">
                  <Button size="small" icon={<ZoomInOutlined />} onClick={handleZoomIn} />
                </Tooltip>

                <Tooltip title="Zoom Out">
                  <Button size="small" icon={<ZoomOutOutlined />} onClick={handleZoomOut} />
                </Tooltip>

                <Tooltip title="Fit View">
                  <Button size="small" icon={<ReloadOutlined />} onClick={handleFitView} />
                </Tooltip>

                <Tooltip title="Download">
                  <Button size="small" icon={<DownloadOutlined />} onClick={handleDownload} />
                </Tooltip>
              </Space>
            }
          >
            <div
              ref={containerRef}
              style={{
                width: "100%",
                height: "calc(100% - 57px)",
                border: "1px solid #d9d9d9",
                borderRadius: "6px",
                position: "relative",
              }}
            >
              {loading && (
                <div style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  zIndex: 1000
                }}>
                  <Spin size="large" />
                </div>
              )}
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default NetworkTopology;
