import React, { useEffect, useRef, useState } from "react";
import {
  Card,
  Button,
  Space,
  Select,
  Tooltip,
  Badge,
  Spin,
  Tree,
  Row,
  Col,
  theme as antdTheme,
} from "antd";
import {
  ZoomInOutlined,
  ZoomOutOutlined,
  ReloadOutlined,
  DownloadOutlined,
  ApartmentOutlined,
  GlobalOutlined,
  EnvironmentOutlined,
  ClusterOutlined,
  GroupOutlined,
  LaptopOutlined,
} from "@ant-design/icons";
import G6 from "@antv/g6";
import { useGetNetworkTopologyDataQuery } from "../app/services/groupsApi";
import { TopologyImage } from "./topology/TopologyImage";
import "./NetworkTopology.css";

const { Option } = Select;

// Constants from TopologyPage
const ANIMATION_CONFIG = { duration: 200, easing: "easeCubic" };
const GRAPH_CONFIG = {
  DEFAULT_NODE_SIZE: [56, 56],
  FORCE_LAYOUT: {
    linkDistance: 250,
    nodeStrength: -50,
    edgeStrength: 0.1,
    collideStrength: 0.8,
    nodeSize: 30,
    alpha: 0.3,
    alphaDecay: 0.028,
    alphaMin: 0.01,
  },
  ANIMATION: {
    repeat: true,
    duration: 3000,
  },
};

// G6 Edge Registration (from TopologyPage)
G6.registerEdge(
  "circle-running",
  {
    afterDraw(cfg, group) {
      const shape = group.get("children")[0];
      const startPoint = shape.getPoint(0);
      const color = cfg.circleColor || "#1890ff";

      if (!startPoint || color === "transparent") return;

      // Remove any existing circles to prevent duplicates
      const existingCircles = group.findAll(
        (element) => element.get("name") === "running-circle"
      );
      existingCircles.forEach((circle) => circle.remove());

      const circle = group.addShape("circle", {
        attrs: {
          x: startPoint.x,
          y: startPoint.y,
          fill: color,
          r: 3,
        },
        name: "running-circle",
      });

      circle.animate((ratio) => {
        const tmpPoint = shape.getPoint(ratio);
        return tmpPoint ? { x: tmpPoint.x, y: tmpPoint.y } : { x: 0, y: 0 };
      }, GRAPH_CONFIG.ANIMATION);
    },
    afterUpdate(cfg, item) {
      const group = item.getContainer();
      this.afterDraw(cfg, group);
    },
    setState(name, value, item) {
      if (name === "refresh" && value) {
        const group = item.getContainer();
        const cfg = item.getModel();
        this.afterDraw(cfg, group);
      }
    },
  },
  "line"
);

const NetworkTopology = () => {
  const { token } = antdTheme.useToken();
  const containerRef = useRef(null);
  const [graph, setGraph] = useState(null);
  const [selectedLayout, setSelectedLayout] = useState("force");
  const [loading, setLoading] = useState(true);
  const [selectedSubnetGroup, setSelectedSubnetGroup] = useState(null);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);

  const {
    data: networkData,
    isLoading,
    error,
  } = useGetNetworkTopologyDataQuery();

  // Extract data from the combined response
  const groups = networkData?.groups || [];
  const topologyData = networkData?.topologyData || {};
  const subnetGroups = networkData?.subnetGroups || [];

  // Debug logging
  useEffect(() => {
    if (networkData) {
      console.log("Network data received:", networkData);
      console.log("Groups:", groups);
      console.log("Topology data keys:", Object.keys(topologyData));
      console.log("Subnet groups:", subnetGroups);
    }
  }, [networkData]);

  // Convert groups data to tree format for left panel
  const convertToTreeData = (groupsData) => {
    console.log("Converting groups data to tree:", groupsData);
    if (!groupsData || groupsData.length === 0) return [];

    const treeData = [];

    groupsData.forEach((relm) => {
      const relmNode = {
        title: (
          <span>
            <ApartmentOutlined style={{ marginRight: 8, color: "#1890ff" }} />
            {relm.name}
          </span>
        ),
        key: `relm-${relm.name}`,
        type: "relm",
        data: relm,
        children: [],
      };

      relm.regions?.forEach((region) => {
        const regionNode = {
          title: (
            <span>
              <GlobalOutlined style={{ marginRight: 8, color: "#52c41a" }} />
              {region.name}
            </span>
          ),
          key: `region-${relm.name}-${region.name}`,
          type: "region",
          data: region,
          children: [],
        };

        region.zones?.forEach((zone) => {
          const zoneNode = {
            title: (
              <span>
                <EnvironmentOutlined
                  style={{ marginRight: 8, color: "#faad14" }}
                />
                {zone.name}
              </span>
            ),
            key: `zone-${relm.name}-${region.name}-${zone.name}`,
            type: "zone",
            data: zone,
            children: [],
          };

          zone.subnets?.forEach((subnet) => {
            const subnetNode = {
              title: (
                <span>
                  <ClusterOutlined
                    style={{ marginRight: 8, color: "#f5222d" }}
                  />
                  {subnet.name}
                </span>
              ),
              key: `subnet-${relm.name}-${region.name}-${zone.name}-${subnet.name}`,
              type: "subnet",
              data: subnet,
              children: [],
            };

            subnet.subnet_groups?.forEach((subnetGroup) => {
              const deviceCount = subnetGroup.devices?.length || 0;
              console.log(
                `SubnetGroup: ${subnetGroup.name}, devices:`,
                subnetGroup.devices
              );
              const subnetGroupNode = {
                title: (
                  <span>
                    <GroupOutlined
                      style={{ marginRight: 8, color: "#722ed1" }}
                    />
                    {subnetGroup.name}
                    <Badge count={deviceCount} style={{ marginLeft: 8 }} />
                  </span>
                ),
                key: `subnetgroup-${relm.name}-${region.name}-${zone.name}-${subnet.name}-${subnetGroup.name}`,
                type: "subnetGroup",
                data: subnetGroup,
                isLeaf: true,
              };

              subnetNode.children.push(subnetGroupNode);
            });

            zoneNode.children.push(subnetNode);
          });

          regionNode.children.push(zoneNode);
        });

        relmNode.children.push(regionNode);
      });

      treeData.push(relmNode);
    });

    return treeData;
  };

  // Convert topology data to G6 graph format for right panel
  const convertToTopologyGraph = (selectedDevices, topologyData) => {
    const nodes = [];
    const edges = [];

    if (!selectedDevices || selectedDevices.length === 0) {
      nodes.push({
        id: "no-selection",
        label: "Select a SubnetGroup\nto view topology",
        type: "rect",
        style: {
          fill: "#f0f0f0",
          stroke: "#d9d9d9",
        },
        labelCfg: {
          style: {
            fill: token.colorTextSecondary,
            fontSize: 16,
          },
        },
      });
      return { nodes, edges };
    }

    const labelStyle = {
      style: {
        fill: token.colorText,
        fontSize: 12,
        background: {
          fill: "transparent",
          padding: [2, 2, 2, 2],
        },
      },
    };

    // Create nodes for selected devices (following TopologyPage format)
    selectedDevices.forEach((deviceMac) => {
      const deviceTopologyData = topologyData[deviceMac] || {};
      console.log(
        `Regular topology device ${deviceMac} data:`,
        deviceTopologyData
      );

      nodes.push({
        id: deviceMac,
        label: `${
          deviceTopologyData.ipAddress || deviceTopologyData.IpAddress || "N/A"
        }\n${deviceMac}\n${
          deviceTopologyData.modelname ||
          deviceTopologyData.ModelName ||
          "Unknown"
        }`,
        type: "image",
        img: TopologyImage(
          deviceTopologyData.modelname || deviceTopologyData.ModelName
        ),
        size: GRAPH_CONFIG.DEFAULT_NODE_SIZE,
        labelCfg: {
          ...labelStyle,
          position: "bottom",
        },
        originalData: {
          mac: deviceMac,
          ipAddress:
            deviceTopologyData.ipAddress || deviceTopologyData.IpAddress,
          modelName:
            deviceTopologyData.modelname || deviceTopologyData.ModelName,
          services: deviceTopologyData.services || deviceTopologyData.Services,
          links:
            deviceTopologyData.linkData || deviceTopologyData.LinkData || [],
        },
      });
    });

    // Create edges between selected devices
    selectedDevices.forEach((deviceMac) => {
      const deviceTopologyData = topologyData[deviceMac] || {};
      const linkData =
        deviceTopologyData.linkData || deviceTopologyData.LinkData || [];

      if (linkData && linkData.length > 0) {
        linkData.forEach((link) => {
          const source = link.source || link.Source;
          const target = link.target || link.Target;
          const sourcePort = link.sourcePort || link.SourcePort;
          const targetPort = link.targetPort || link.TargetPort;
          const linkType = link.linkType || link.LinkType;
          const blockedPort = link.blockedPort || link.BlockedPort;

          if (
            source &&
            target &&
            source !== target &&
            selectedDevices.includes(target)
          ) {
            console.log(`Creating regular edge: ${source} -> ${target}`);
            edges.push({
              id: `${source}-${target}`,
              source: source,
              target: target,
              label: `${source}_${sourcePort}\n${target}_${targetPort}`,
              type: "circle-running",
              color:
                blockedPort === "true"
                  ? "#faad14"
                  : linkType === "manual"
                  ? "#722ed1"
                  : token.colorTextDisabled,
              circleColor:
                blockedPort === "true" ||
                linkType === "dashed" ||
                linkType === "manual"
                  ? "transparent"
                  : token.colorPrimary,
              labelCfg: labelStyle,
              style: {
                lineWidth: 2,
                lineDash:
                  linkType === "dashed" || linkType === "manual"
                    ? [4, 4]
                    : undefined,
              },
              originalData: {
                sourcePort: sourcePort,
                targetPort: targetPort,
                linkType: linkType,
                blockedPort: blockedPort,
              },
            });
          }
        });
      }
    });

    return { nodes, edges };
  };

  // Handle tree selection
  const handleTreeSelect = (selectedKeys, info) => {
    console.log("Tree selection:", {
      selectedKeys,
      nodeType: info.node?.type,
      nodeData: info.node?.data,
    });
    setSelectedKeys(selectedKeys);

    if (info.node && info.node.type === "subnetGroup") {
      const subnetGroupData = info.node.data;
      console.log("Selected SubnetGroup:", subnetGroupData);

      // Find the corresponding subnet group topology data from the API response
      const keyParts = info.node.key.split("-");
      const subnetGroupTopology = subnetGroups.find(
        (sg) =>
          sg.name === subnetGroupData.name &&
          sg.relmName === keyParts[1] &&
          sg.regionName === keyParts[2] &&
          sg.zoneName === keyParts[3] &&
          sg.subnetName === keyParts[4]
      );

      if (subnetGroupTopology) {
        console.log("Found subnet group topology:", subnetGroupTopology);
        setSelectedSubnetGroup(subnetGroupTopology);

        // Update topology graph with pre-processed data
        if (graph) {
          console.log(
            "Updating topology with devices:",
            subnetGroupTopology.devices
          );
          updateTopologyGraphWithSubnetGroup(subnetGroupTopology);
        }
      } else {
        console.log("Subnet group topology not found, using basic data");
        setSelectedSubnetGroup(subnetGroupData);
        if (graph && subnetGroupData.devices) {
          updateTopologyGraph(subnetGroupData.devices);
        }
      }
    } else {
      console.log("Non-subnetGroup selected, clearing topology");
      setSelectedSubnetGroup(null);
      if (graph) {
        updateTopologyGraph([]);
      }
    }
  };

  // Auto-expand all tree nodes
  useEffect(() => {
    if (groups && groups.length > 0) {
      const treeData = convertToTreeData(groups);
      const allKeys = [];

      const extractKeys = (nodes) => {
        nodes.forEach((node) => {
          allKeys.push(node.key);
          if (node.children && node.children.length > 0) {
            extractKeys(node.children);
          }
        });
      };

      extractKeys(treeData);
      setExpandedKeys(allKeys);
    }
  }, [groups]);

  // Initialize graph (based on TopologyPage)
  const initGraph = () => {
    if (!containerRef.current) return;

    const width = containerRef.current.offsetWidth;
    const height = containerRef.current.offsetHeight || 400;

    const newGraph = new G6.Graph({
      container: containerRef.current,
      width,
      height,
      renderer: "svg",
      linkCenter: true,
      animate: true,
      modes: {
        default: ["zoom-canvas", "drag-canvas", "click-select", "drag-node"],
      },
      defaultNode: {
        type: "image",
        size: GRAPH_CONFIG.DEFAULT_NODE_SIZE,
      },
      defaultEdge: {
        type: "circle-running",
      },
      layout: {
        type: "force",
        ...GRAPH_CONFIG.FORCE_LAYOUT,
      },
    });

    setGraph(newGraph);
    return newGraph;
  };

  // Update topology graph with selected devices
  const updateTopologyGraph = (selectedDevices) => {
    if (!graph) return;

    const graphData = convertToTopologyGraph(selectedDevices, topologyData);
    console.log("Regular topology update with data:", graphData);

    if (graphData && graphData.nodes) {
      // Clear existing data first
      graph.clear();

      // Set new data
      graph.data(graphData);

      // Re-render the graph
      graph.render();

      // Force layout update
      graph.updateLayout();

      // Fit view after a short delay
      setTimeout(() => {
        graph.fitView();

        // Force refresh edge animations after render
        graph.getEdges().forEach((edge) => {
          edge.setState("refresh", true);
          edge.setState("refresh", false);
        });
      }, 200);
    }
  };

  // Update topology graph with pre-processed subnet group data
  const updateTopologyGraphWithSubnetGroup = (subnetGroupTopology) => {
    if (!graph) return;

    const nodes = [];
    const edges = [];

    const labelStyle = {
      style: {
        fill: token.colorText,
        fontSize: 12,
        background: {
          fill: "transparent",
          padding: [2, 2, 2, 2],
        },
      },
    };

    // Create nodes from pre-processed device data
    subnetGroupTopology.devices.forEach((deviceMac) => {
      const deviceData = subnetGroupTopology.deviceData[deviceMac] || {};
      console.log(`Device ${deviceMac} data:`, deviceData);

      nodes.push({
        id: deviceMac,
        label: `${
          deviceData.ipAddress || deviceData.IpAddress || "N/A"
        }\n${deviceMac}\n${
          deviceData.modelname || deviceData.ModelName || "Unknown"
        }`,
        type: "image",
        img: TopologyImage(deviceData.modelname || deviceData.ModelName),
        size: GRAPH_CONFIG.DEFAULT_NODE_SIZE,
        labelCfg: {
          ...labelStyle,
          position: "bottom",
        },
        originalData: deviceData,
      });
    });

    // Create edges from pre-processed connections
    subnetGroupTopology.connections.forEach((connection) => {
      edges.push({
        id: `${connection.source}-${connection.target}`,
        source: connection.source,
        target: connection.target,
        label: `${connection.source}_${connection.sourcePort}\n${connection.target}_${connection.targetPort}`,
        type: "circle-running",
        color:
          connection.blockedPort === "true"
            ? "#faad14"
            : connection.linkType === "manual"
            ? "#722ed1"
            : token.colorTextDisabled,
        circleColor:
          connection.blockedPort === "true" ||
          connection.linkType === "dashed" ||
          connection.linkType === "manual"
            ? "transparent"
            : token.colorPrimary,
        labelCfg: labelStyle,
        style: {
          lineWidth: 2,
          lineDash:
            connection.linkType === "dashed" || connection.linkType === "manual"
              ? [4, 4]
              : undefined,
        },
        originalData: connection,
      });
    });

    const graphData = { nodes, edges };
    console.log("Updating graph with data:", graphData);

    if (graphData && graphData.nodes && graphData.nodes.length > 0) {
      // Clear existing data first
      graph.clear();

      // Set new data
      graph.data(graphData);

      // Re-render the graph
      graph.render();

      // Force layout update
      graph.updateLayout();

      // Fit view after a short delay to ensure layout is complete
      setTimeout(() => {
        graph.fitView();

        // Force refresh edge animations after render
        graph.getEdges().forEach((edge) => {
          edge.setState("refresh", true);
          edge.setState("refresh", false);
        });
      }, 200);
    } else if (graphData && graphData.nodes && graphData.nodes.length === 0) {
      // Handle empty state
      graph.clear();
      const emptyData = {
        nodes: [
          {
            id: "no-devices",
            label: "No devices in this SubnetGroup",
            type: "rect",
            style: {
              fill: "#f0f0f0",
              stroke: "#d9d9d9",
            },
          },
        ],
        edges: [],
      };
      graph.data(emptyData);
      graph.render();
      graph.fitView();
    }
  };

  // Control handlers
  const handleZoomIn = () => graph?.zoomTo(graph.getZoom() * 1.2);
  const handleZoomOut = () => graph?.zoomTo(graph.getZoom() * 0.8);
  const handleFitView = () => {
    if (graph && containerRef.current) {
      // Update graph size to current container dimensions
      const width = containerRef.current.offsetWidth;
      const height = containerRef.current.offsetHeight;

      if (width > 0 && height > 0) {
        graph.changeSize(width, height);
        graph.render();
      }

      graph.fitView();
    }
  };
  const handleDownload = () => {
    if (graph) {
      graph.downloadFullImage("network-topology", "image/png");
    }
  };

  // Initialize graph on mount
  useEffect(() => {
    if (containerRef.current && !graph && !isLoading && networkData) {
      console.log("Initializing graph...");
      const newGraph = initGraph();
      if (newGraph) {
        const graphData = convertToTopologyGraph([], topologyData);
        newGraph.data(graphData);
        newGraph.render();
        setLoading(false);
        console.log("Graph initialized successfully");
      }
    }
  }, [graph, isLoading, networkData, topologyData]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (graph) {
        graph.destroy();
      }
    };
  }, []);

  // Handle container resize
  useEffect(() => {
    if (!containerRef.current || !graph) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        console.log(`Container resized to: ${width}x${height}`);

        if (width > 0 && height > 0) {
          // Update graph dimensions
          graph.changeSize(width, height);

          // Re-render with current data
          graph.render();

          // Fit view to new dimensions
          setTimeout(() => {
            graph.fitView();
          }, 100);
        }
      }
    });

    resizeObserver.observe(containerRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [graph]);

  // Handle window resize as fallback
  useEffect(() => {
    if (!graph) return;

    const handleWindowResize = () => {
      if (containerRef.current) {
        const width = containerRef.current.offsetWidth;
        const height = containerRef.current.offsetHeight;

        console.log(`Window resize - updating graph to: ${width}x${height}`);

        if (width > 0 && height > 0) {
          graph.changeSize(width, height);
          graph.render();

          setTimeout(() => {
            graph.fitView();
          }, 100);
        }
      }
    };

    window.addEventListener("resize", handleWindowResize);

    return () => {
      window.removeEventListener("resize", handleWindowResize);
    };
  }, [graph]);

  // Update topology when data changes
  useEffect(() => {
    if (graph && selectedSubnetGroup) {
      // Check if selectedSubnetGroup has pre-processed data
      if (selectedSubnetGroup.deviceData && selectedSubnetGroup.connections) {
        updateTopologyGraphWithSubnetGroup(selectedSubnetGroup);
      } else {
        updateTopologyGraph(selectedSubnetGroup.devices || []);
      }
    }
  }, [networkData, selectedSubnetGroup]);

  // Timeout mechanism to prevent infinite loading
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (loading) {
        console.log("Forcing loading to false after timeout");
        setLoading(false);
      }
    }, 5000); // 5 second timeout

    return () => clearTimeout(timeout);
  }, [loading]);

  if (isLoading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
        }}
      >
        <Spin size="large" />
        <span style={{ marginLeft: 16 }}>Loading network data...</span>
      </div>
    );
  }

  return (
    <div>
      <Row gutter={16} style={{ height: "100%" }}>
        {/* Left Panel - Group Hierarchy Tree */}
        <Col span={7}>
          <Card
            title={
              <span>
                <ApartmentOutlined style={{ marginRight: 8 }} />
                Group Hierarchy
              </span>
            }
            style={{ height: "100%" }}
            styles={{
              body: {
                height: "calc(100vh - 158px)",
                overflow: "auto",
                padding: 10,
              },
            }}
          >
            {groups && groups.length > 0 ? (
              <Tree
                showIcon
                defaultExpandAll
                expandedKeys={expandedKeys}
                selectedKeys={selectedKeys}
                onSelect={handleTreeSelect}
                treeData={convertToTreeData(groups)}
                style={{ fontSize: "14px" }}
              />
            ) : (
              <div
                style={{ textAlign: "center", padding: "20px", color: "#999" }}
              >
                <ApartmentOutlined
                  style={{ fontSize: "48px", marginBottom: "16px" }}
                />
                <div>No group hierarchy available</div>
              </div>
            )}
          </Card>
        </Col>

        {/* Right Panel - Device Topology */}
        <Col span={17}>
          <Card
            title={
              <span>
                <LaptopOutlined style={{ marginRight: 8 }} />
                Device Topology
                {selectedSubnetGroup && (
                  <span
                    style={{
                      marginLeft: 16,
                      color: "#722ed1",
                      fontSize: "14px",
                    }}
                  >
                    ({selectedSubnetGroup.name} -{" "}
                    {selectedSubnetGroup.devices?.length || 0} devices)
                  </span>
                )}
              </span>
            }
            style={{ height: "100%" }}
            styles={{ body: { padding: 5 } }}
            extra={
              <Space>
                <Tooltip title="Zoom In">
                  <Button
                    size="small"
                    icon={<ZoomInOutlined />}
                    onClick={handleZoomIn}
                  />
                </Tooltip>

                <Tooltip title="Zoom Out">
                  <Button
                    size="small"
                    icon={<ZoomOutOutlined />}
                    onClick={handleZoomOut}
                  />
                </Tooltip>

                <Tooltip title="Fit View">
                  <Button
                    size="small"
                    icon={<ReloadOutlined />}
                    onClick={handleFitView}
                  />
                </Tooltip>

                <Tooltip title="Download">
                  <Button
                    size="small"
                    icon={<DownloadOutlined />}
                    onClick={handleDownload}
                  />
                </Tooltip>
              </Space>
            }
          >
            <div
              ref={containerRef}
              style={{
                width: "100%",
                height: "calc(100vh - 150px)",
                minHeight: "400px",
                border: "1px solid #d9d9d9",
                borderRadius: "6px",
                position: "relative",
                backgroundColor: "#fafafa",
              }}
            >
              {loading && (
                <div
                  style={{
                    position: "absolute",
                    top: "50%",
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                    zIndex: 1000,
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    gap: "12px",
                  }}
                >
                  <Spin size="large" />
                  <span style={{ color: "#666", fontSize: "14px" }}>
                    Loading topology...
                  </span>
                </div>
              )}
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default NetworkTopology;
