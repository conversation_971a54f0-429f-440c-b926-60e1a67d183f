package mnms

import (
	"fmt"
	"time"

	"github.com/qeof/q"
)

// Subnet represents a subnet within a zone containing devices
type Subnet struct {
	Name    string   `json:"name"`
	Comment string   `json:"comment,omitempty"`
	Image   string   `json:"image,omitempty"`
	Devices []string `json:"devices"` // array of MAC addresses or device identifiers
}

// Zone represents a zone within a region containing subnets
type Zone struct {
	Name    string   `json:"name"`
	Comment string   `json:"comment,omitempty"`
	Image   string   `json:"image,omitempty"`
	NmsSvc  string   `json:"nmssvc,omitempty"` // network management service
	Subnets []Subnet `json:"subnets"`
}

// Region represents a region within a relm containing zones
type Region struct {
	Name    string `json:"name"`
	Comment string `json:"comment,omitempty"`
	Image   string `json:"image,omitempty"`
	Zones   []Zone `json:"zones"`
}

// Relm represents the top-level organizational structure
type Relm struct {
	Name      string   `json:"name"`
	Comment   string   `json:"comment,omitempty"`
	Location  string   `json:"location,omitempty"`
	Image     string   `json:"image,omitempty"`
	Regions   []Region `json:"regions"`
	CreatedAt string   `json:"created_at"`
	UpdatedAt string   `json:"updated_at"`
}

// GroupManager handles all group-related operations
type GroupManager struct {
	// No longer needs its own storage, uses QC.GroupData
}

// Global group manager instance
var GM *GroupManager

func init() {
	GM = &GroupManager{}
}

// CreateRelm creates a new relm
func (gm *GroupManager) CreateRelm(relm *Relm) error {
	QC.GroupMutex.Lock()
	defer QC.GroupMutex.Unlock()

	if _, exists := QC.GroupData[relm.Name]; exists {
		return fmt.Errorf("relm '%s' already exists", relm.Name)
	}

	now := time.Now().Format(time.RFC3339)
	relm.CreatedAt = now
	relm.UpdatedAt = now

	QC.GroupData[relm.Name] = relm

	// Persist to MNMS config
	return gm.persistGroups()
}

// GetRelm retrieves a relm by name
func (gm *GroupManager) GetRelm(name string) (*Relm, error) {
	QC.GroupMutex.Lock()
	defer QC.GroupMutex.Unlock()

	relm, exists := QC.GroupData[name]
	if !exists {
		return nil, fmt.Errorf("relm '%s' not found", name)
	}

	return relm, nil
}

// ListRelms returns all relms
func (gm *GroupManager) ListRelms() map[string]*Relm {
	QC.GroupMutex.Lock()
	defer QC.GroupMutex.Unlock()

	result := make(map[string]*Relm)
	for k, v := range QC.GroupData {
		result[k] = v
	}
	return result
}

// UpdateRelm updates an existing relm
func (gm *GroupManager) UpdateRelm(relm *Relm) error {
	QC.GroupMutex.Lock()
	defer QC.GroupMutex.Unlock()

	if _, exists := QC.GroupData[relm.Name]; !exists {
		return fmt.Errorf("relm '%s' not found", relm.Name)
	}

	relm.UpdatedAt = time.Now().Format(time.RFC3339)
	QC.GroupData[relm.Name] = relm

	// Persist to MNMS config
	return gm.persistGroups()
}

// DeleteRelm deletes a relm
func (gm *GroupManager) DeleteRelm(name string) error {
	QC.GroupMutex.Lock()
	defer QC.GroupMutex.Unlock()

	if _, exists := QC.GroupData[name]; !exists {
		return fmt.Errorf("relm '%s' not found", name)
	}

	delete(QC.GroupData, name)

	// Persist to MNMS config
	return gm.persistGroups()
}

// persistGroups saves all groups to MNMS config
func (gm *GroupManager) persistGroups() error {
	// Get current MNMS config
	config, err := GetMNMSConfig()
	if err != nil {
		q.Q("Failed to get MNMS config:", err)
		return err
	}

	// Update groups in config
	config.Groups = make(map[string]*Relm)
	for name, relm := range QC.GroupData {
		config.Groups[name] = relm
	}

	// Write updated config
	err = WriteMNMSConfig(config)
	if err != nil {
		q.Q("Failed to write MNMS config:", err)
		return err
	}

	return nil
}

// LoadGroupsFromConfig loads all groups from MNMS config on startup
func (gm *GroupManager) LoadGroupsFromConfig() error {
	config, err := GetMNMSConfig()
	if err != nil {
		q.Q("Failed to get MNMS config:", err)
		return err
	}

	QC.GroupMutex.Lock()
	defer QC.GroupMutex.Unlock()

	// Initialize groups if not present in config
	if config.Groups == nil {
		QC.GroupData = make(map[string]*Relm)
		return nil
	}

	// Load groups from config
	QC.GroupData = make(map[string]*Relm)
	for name, relm := range config.Groups {
		QC.GroupData[name] = relm
	}

	q.Q("Loaded", len(QC.GroupData), "groups from config")
	return nil
}

// AddRegionToRelm adds a region to an existing relm
func (gm *GroupManager) AddRegionToRelm(relmName string, region Region) error {
	QC.GroupMutex.Lock()
	defer QC.GroupMutex.Unlock()

	relm, exists := QC.GroupData[relmName]
	if !exists {
		return fmt.Errorf("relm '%s' not found", relmName)
	}

	// Check if region already exists
	for _, r := range relm.Regions {
		if r.Name == region.Name {
			return fmt.Errorf("region '%s' already exists in relm '%s'", region.Name, relmName)
		}
	}

	relm.Regions = append(relm.Regions, region)
	relm.UpdatedAt = time.Now().Format(time.RFC3339)

	return gm.persistGroups()
}

// AddZoneToRegion adds a zone to an existing region
func (gm *GroupManager) AddZoneToRegion(relmName, regionName string, zone Zone) error {
	QC.GroupMutex.Lock()
	defer QC.GroupMutex.Unlock()

	relm, exists := QC.GroupData[relmName]
	if !exists {
		return fmt.Errorf("relm '%s' not found", relmName)
	}

	// Find the region
	for i, region := range relm.Regions {
		if region.Name == regionName {
			// Check if zone already exists
			for _, z := range region.Zones {
				if z.Name == zone.Name {
					return fmt.Errorf("zone '%s' already exists in region '%s'", zone.Name, regionName)
				}
			}
			relm.Regions[i].Zones = append(relm.Regions[i].Zones, zone)
			relm.UpdatedAt = time.Now().Format(time.RFC3339)
			return gm.persistGroups()
		}
	}

	return fmt.Errorf("region '%s' not found in relm '%s'", regionName, relmName)
}

// AddSubnetToZone adds a subnet to an existing zone
func (gm *GroupManager) AddSubnetToZone(relmName, regionName, zoneName string, subnet Subnet) error {
	QC.GroupMutex.Lock()
	defer QC.GroupMutex.Unlock()

	relm, exists := QC.GroupData[relmName]
	if !exists {
		return fmt.Errorf("relm '%s' not found", relmName)
	}

	// Find the region and zone
	for i, region := range relm.Regions {
		if region.Name == regionName {
			for j, zone := range region.Zones {
				if zone.Name == zoneName {
					// Check if subnet already exists
					for _, s := range zone.Subnets {
						if s.Name == subnet.Name {
							return fmt.Errorf("subnet '%s' already exists in zone '%s'", subnet.Name, zoneName)
						}
					}
					relm.Regions[i].Zones[j].Subnets = append(relm.Regions[i].Zones[j].Subnets, subnet)
					relm.UpdatedAt = time.Now().Format(time.RFC3339)
					return gm.persistGroups()
				}
			}
			return fmt.Errorf("zone '%s' not found in region '%s'", zoneName, regionName)
		}
	}

	return fmt.Errorf("region '%s' not found in relm '%s'", regionName, relmName)
}

// AddDeviceToSubnet adds a device to an existing subnet
func (gm *GroupManager) AddDeviceToSubnet(relmName, regionName, zoneName, subnetName, deviceMac string) error {
	QC.GroupMutex.Lock()
	defer QC.GroupMutex.Unlock()

	relm, exists := QC.GroupData[relmName]
	if !exists {
		return fmt.Errorf("relm '%s' not found", relmName)
	}

	// Find the region, zone, and subnet
	for i, region := range relm.Regions {
		if region.Name == regionName {
			for j, zone := range region.Zones {
				if zone.Name == zoneName {
					for k, subnet := range zone.Subnets {
						if subnet.Name == subnetName {
							// Check if device already exists
							for _, device := range subnet.Devices {
								if device == deviceMac {
									return fmt.Errorf("device '%s' already exists in subnet '%s'", deviceMac, subnetName)
								}
							}
							relm.Regions[i].Zones[j].Subnets[k].Devices = append(relm.Regions[i].Zones[j].Subnets[k].Devices, deviceMac)
							relm.UpdatedAt = time.Now().Format(time.RFC3339)

							// Update device info with subnet name
							gm.updateDeviceSubnetInfo(deviceMac, subnetName)

							return gm.persistGroups()
						}
					}
					return fmt.Errorf("subnet '%s' not found in zone '%s'", subnetName, zoneName)
				}
			}
			return fmt.Errorf("zone '%s' not found in region '%s'", zoneName, regionName)
		}
	}

	return fmt.Errorf("region '%s' not found in relm '%s'", regionName, relmName)
}

// RemoveDeviceFromSubnet removes a device from a subnet
func (gm *GroupManager) RemoveDeviceFromSubnet(relmName, regionName, zoneName, subnetName, deviceMac string) error {
	QC.GroupMutex.Lock()
	defer QC.GroupMutex.Unlock()

	relm, exists := QC.GroupData[relmName]
	if !exists {
		return fmt.Errorf("relm '%s' not found", relmName)
	}

	// Find the region, zone, and subnet
	for i, region := range relm.Regions {
		if region.Name == regionName {
			for j, zone := range region.Zones {
				if zone.Name == zoneName {
					for k, subnet := range zone.Subnets {
						if subnet.Name == subnetName {
							// Find and remove device
							for l, device := range subnet.Devices {
								if device == deviceMac {
									relm.Regions[i].Zones[j].Subnets[k].Devices = append(
										subnet.Devices[:l],
										subnet.Devices[l+1:]...,
									)
									relm.UpdatedAt = time.Now().Format(time.RFC3339)

									// Clear device subnet info
									gm.updateDeviceSubnetInfo(deviceMac, "")

									return gm.persistGroups()
								}
							}
							return fmt.Errorf("device '%s' not found in subnet '%s'", deviceMac, subnetName)
						}
					}
					return fmt.Errorf("subnet '%s' not found in zone '%s'", subnetName, zoneName)
				}
			}
			return fmt.Errorf("zone '%s' not found in region '%s'", zoneName, regionName)
		}
	}

	return fmt.Errorf("region '%s' not found in relm '%s'", regionName, relmName)
}

// ValidateRelm validates a relm structure
func ValidateRelm(relm *Relm) error {
	if relm.Name == "" {
		return fmt.Errorf("relm name cannot be empty")
	}

	// Validate name format (alphanumeric, hyphens, underscores only)
	if !isValidName(relm.Name) {
		return fmt.Errorf("relm name must contain only alphanumeric characters, hyphens, and underscores")
	}

	// Validate regions
	regionNames := make(map[string]bool)
	for _, region := range relm.Regions {
		if err := ValidateRegion(&region); err != nil {
			return fmt.Errorf("invalid region '%s': %v", region.Name, err)
		}

		if regionNames[region.Name] {
			return fmt.Errorf("duplicate region name '%s'", region.Name)
		}
		regionNames[region.Name] = true
	}

	return nil
}

// ValidateRegion validates a region structure
func ValidateRegion(region *Region) error {
	if region.Name == "" {
		return fmt.Errorf("region name cannot be empty")
	}

	if !isValidName(region.Name) {
		return fmt.Errorf("region name must contain only alphanumeric characters, hyphens, and underscores")
	}

	// Validate zones
	zoneNames := make(map[string]bool)
	for _, zone := range region.Zones {
		if err := ValidateZone(&zone); err != nil {
			return fmt.Errorf("invalid zone '%s': %v", zone.Name, err)
		}

		if zoneNames[zone.Name] {
			return fmt.Errorf("duplicate zone name '%s'", zone.Name)
		}
		zoneNames[zone.Name] = true
	}

	return nil
}

// ValidateZone validates a zone structure
func ValidateZone(zone *Zone) error {
	if zone.Name == "" {
		return fmt.Errorf("zone name cannot be empty")
	}

	if !isValidName(zone.Name) {
		return fmt.Errorf("zone name must contain only alphanumeric characters, hyphens, and underscores")
	}

	// Validate subnets
	subnetNames := make(map[string]bool)
	for _, subnet := range zone.Subnets {
		if err := ValidateSubnet(&subnet); err != nil {
			return fmt.Errorf("invalid subnet '%s': %v", subnet.Name, err)
		}

		if subnetNames[subnet.Name] {
			return fmt.Errorf("duplicate subnet name '%s'", subnet.Name)
		}
		subnetNames[subnet.Name] = true
	}

	return nil
}

// ValidateSubnet validates a subnet structure
func ValidateSubnet(subnet *Subnet) error {
	if subnet.Name == "" {
		return fmt.Errorf("subnet name cannot be empty")
	}

	if !isValidName(subnet.Name) {
		return fmt.Errorf("subnet name must contain only alphanumeric characters, hyphens, and underscores")
	}

	// Validate devices (MAC addresses)
	deviceMacs := make(map[string]bool)
	for _, deviceMac := range subnet.Devices {
		if !isValidMacAddress(deviceMac) {
			return fmt.Errorf("invalid MAC address format: %s", deviceMac)
		}

		if deviceMacs[deviceMac] {
			return fmt.Errorf("duplicate device MAC address '%s'", deviceMac)
		}
		deviceMacs[deviceMac] = true
	}

	return nil
}

// isValidName checks if a name contains only valid characters
func isValidName(name string) bool {
	if len(name) == 0 || len(name) > 64 {
		return false
	}

	for _, char := range name {
		if !((char >= 'a' && char <= 'z') ||
			(char >= 'A' && char <= 'Z') ||
			(char >= '0' && char <= '9') ||
			char == '-' || char == '_') {
			return false
		}
	}

	return true
}

// isValidMacAddress validates MAC address format
func isValidMacAddress(mac string) bool {
	if len(mac) != 17 {
		return false
	}

	// Check format: XX-XX-XX-XX-XX-XX
	for i, char := range mac {
		if i%3 == 2 {
			if char != '-' {
				return false
			}
		} else {
			if !((char >= '0' && char <= '9') ||
				(char >= 'A' && char <= 'F') ||
				(char >= 'a' && char <= 'f')) {
				return false
			}
		}
	}

	return true
}

// FindDeviceInGroups finds which group/subnet contains a specific device
func (gm *GroupManager) FindDeviceInGroups(deviceMac string) (string, string, string, string, error) {
	QC.GroupMutex.Lock()
	defer QC.GroupMutex.Unlock()

	for relmName, relm := range QC.GroupData {
		for _, region := range relm.Regions {
			for _, zone := range region.Zones {
				for _, subnet := range zone.Subnets {
					for _, device := range subnet.Devices {
						if device == deviceMac {
							return relmName, region.Name, zone.Name, subnet.Name, nil
						}
					}
				}
			}
		}
	}

	return "", "", "", "", fmt.Errorf("device '%s' not found in any group", deviceMac)
}

// GetDevicesInSubnet returns all devices in a specific subnet
func (gm *GroupManager) GetDevicesInSubnet(relmName, regionName, zoneName, subnetName string) ([]string, error) {
	QC.GroupMutex.Lock()
	defer QC.GroupMutex.Unlock()

	relm, exists := QC.GroupData[relmName]
	if !exists {
		return nil, fmt.Errorf("relm '%s' not found", relmName)
	}

	for _, region := range relm.Regions {
		if region.Name == regionName {
			for _, zone := range region.Zones {
				if zone.Name == zoneName {
					for _, subnet := range zone.Subnets {
						if subnet.Name == subnetName {
							return subnet.Devices, nil
						}
					}
					return nil, fmt.Errorf("subnet '%s' not found in zone '%s'", subnetName, zoneName)
				}
			}
			return nil, fmt.Errorf("zone '%s' not found in region '%s'", zoneName, regionName)
		}
	}

	return nil, fmt.Errorf("region '%s' not found in relm '%s'", regionName, relmName)
}

// GetGroupHierarchy returns the complete hierarchy for a relm
func (gm *GroupManager) GetGroupHierarchy(relmName string) (*Relm, error) {
	return gm.GetRelm(relmName)
}

// ValidateDeviceExists checks if a device exists in the system
func ValidateDeviceExists(deviceMac string) error {
	QC.DevMutex.Lock()
	defer QC.DevMutex.Unlock()

	if _, exists := QC.DevData[deviceMac]; !exists {
		return fmt.Errorf("device '%s' does not exist in the system", deviceMac)
	}

	return nil
}

// updateDeviceSubnetInfo updates the subnet name in device info
func (gm *GroupManager) updateDeviceSubnetInfo(deviceMac, subnetName string) {
	QC.DevMutex.Lock()
	defer QC.DevMutex.Unlock()

	if device, exists := QC.DevData[deviceMac]; exists {
		device.SubnetName = subnetName
		QC.DevData[deviceMac] = device
		q.Q("Updated device", deviceMac, "subnet to", subnetName)
	} else {
		q.Q("Device", deviceMac, "not found in DevData")
	}
}

// GetAllDevicesGrouped returns all devices organized by their group hierarchy
func (gm *GroupManager) GetAllDevicesGrouped() map[string]map[string]map[string]map[string][]DevInfo {
	QC.GroupMutex.Lock()
	QC.DevMutex.Lock()
	defer QC.GroupMutex.Unlock()
	defer QC.DevMutex.Unlock()

	result := make(map[string]map[string]map[string]map[string][]DevInfo)

	for relmName, relm := range QC.GroupData {
		result[relmName] = make(map[string]map[string]map[string][]DevInfo)

		for _, region := range relm.Regions {
			result[relmName][region.Name] = make(map[string]map[string][]DevInfo)

			for _, zone := range region.Zones {
				result[relmName][region.Name][zone.Name] = make(map[string][]DevInfo)

				for _, subnet := range zone.Subnets {
					var devices []DevInfo
					for _, deviceMac := range subnet.Devices {
						if device, exists := QC.DevData[deviceMac]; exists {
							devices = append(devices, device)
						}
					}
					result[relmName][region.Name][zone.Name][subnet.Name] = devices
				}
			}
		}
	}

	return result
}

// GetUnassignedDevices returns devices that are not assigned to any subnet
func (gm *GroupManager) GetUnassignedDevices() []DevInfo {
	QC.GroupMutex.Lock()
	QC.DevMutex.Lock()
	defer QC.GroupMutex.Unlock()
	defer QC.DevMutex.Unlock()

	// Create a set of assigned devices
	assignedDevices := make(map[string]bool)
	for _, relm := range QC.GroupData {
		for _, region := range relm.Regions {
			for _, zone := range region.Zones {
				for _, subnet := range zone.Subnets {
					for _, deviceMac := range subnet.Devices {
						assignedDevices[deviceMac] = true
					}
				}
			}
		}
	}

	// Find unassigned devices
	var unassigned []DevInfo
	for deviceMac, device := range QC.DevData {
		if !assignedDevices[deviceMac] {
			unassigned = append(unassigned, device)
		}
	}

	return unassigned
}

// GetDevicesForTransfer returns devices formatted for Ant Design Transfer component
func (gm *GroupManager) GetDevicesForTransfer() []map[string]interface{} {
	QC.DevMutex.Lock()
	defer QC.DevMutex.Unlock()

	var devices []map[string]interface{}
	for _, device := range QC.DevData {
		deviceData := map[string]interface{}{
			"key":         device.Mac,
			"title":       device.Mac,
			"description": fmt.Sprintf("IP: %s | Model: %s", device.IPAddress, device.ModelName),
			"macAddress":  device.Mac,
			"ipAddress":   device.IPAddress,
			"model":       device.ModelName,
			"subnetName":  device.SubnetName,
		}
		devices = append(devices, deviceData)
	}

	return devices
}

// GetAvailableDevicesForSubnet returns devices that can be added to a subnet (not already assigned)
func (gm *GroupManager) GetAvailableDevicesForSubnet(excludeSubnet string) []map[string]interface{} {
	QC.GroupMutex.Lock()
	QC.DevMutex.Lock()
	defer QC.GroupMutex.Unlock()
	defer QC.DevMutex.Unlock()

	// Create a set of assigned devices (excluding the specified subnet)
	assignedDevices := make(map[string]bool)
	for _, relm := range QC.GroupData {
		for _, region := range relm.Regions {
			for _, zone := range region.Zones {
				for _, subnet := range zone.Subnets {
					if subnet.Name != excludeSubnet {
						for _, deviceMac := range subnet.Devices {
							assignedDevices[deviceMac] = true
						}
					}
				}
			}
		}
	}

	// Find available devices
	var available []map[string]interface{}
	for _, device := range QC.DevData {
		if !assignedDevices[device.Mac] {
			deviceData := map[string]interface{}{
				"key":         device.Mac,
				"title":       device.Mac,
				"description": fmt.Sprintf("IP: %s | Model: %s", device.IPAddress, device.ModelName),
				"macAddress":  device.Mac,
				"ipAddress":   device.IPAddress,
				"model":       device.ModelName,
				"subnetName":  device.SubnetName,
			}
			available = append(available, deviceData)
		}
	}

	return available
}

// GetDevicesBySubnet returns devices in a specific subnet with full device info
func (gm *GroupManager) GetDevicesBySubnet(relmName, regionName, zoneName, subnetName string) ([]DevInfo, error) {
	deviceMacs, err := gm.GetDevicesInSubnet(relmName, regionName, zoneName, subnetName)
	if err != nil {
		return nil, err
	}

	QC.DevMutex.Lock()
	defer QC.DevMutex.Unlock()

	var devices []DevInfo
	for _, deviceMac := range deviceMacs {
		if device, exists := QC.DevData[deviceMac]; exists {
			devices = append(devices, device)
		}
	}

	return devices, nil
}

// MoveDeviceBetweenSubnets moves a device from one subnet to another
func (gm *GroupManager) MoveDeviceBetweenSubnets(deviceMac string, fromRealm, fromRegion, fromZone, fromSubnet, toRealm, toRegion, toZone, toSubnet string) error {
	// Remove from source subnet
	err := gm.RemoveDeviceFromSubnet(fromRealm, fromRegion, fromZone, fromSubnet, deviceMac)
	if err != nil {
		return fmt.Errorf("failed to remove device from source subnet: %v", err)
	}

	// Add to destination subnet
	err = gm.AddDeviceToSubnet(toRealm, toRegion, toZone, toSubnet, deviceMac)
	if err != nil {
		// Try to rollback - add back to source subnet
		rollbackErr := gm.AddDeviceToSubnet(fromRealm, fromRegion, fromZone, fromSubnet, deviceMac)
		if rollbackErr != nil {
			q.Q("Failed to rollback device move:", rollbackErr)
		}
		return fmt.Errorf("failed to add device to destination subnet: %v", err)
	}

	return nil
}

// GetGroupStatistics returns statistics about groups and devices
func (gm *GroupManager) GetGroupStatistics() map[string]interface{} {
	QC.GroupMutex.Lock()
	QC.DevMutex.Lock()
	defer QC.GroupMutex.Unlock()
	defer QC.DevMutex.Unlock()

	stats := map[string]interface{}{
		"totalRelms":        len(QC.GroupData),
		"totalRegions":      0,
		"totalZones":        0,
		"totalSubnets":      0,
		"totalDevices":      len(QC.DevData),
		"assignedDevices":   0,
		"unassignedDevices": 0,
	}

	assignedDevices := make(map[string]bool)
	totalRegions := 0
	totalZones := 0
	totalSubnets := 0

	for _, relm := range QC.GroupData {
		totalRegions += len(relm.Regions)
		for _, region := range relm.Regions {
			totalZones += len(region.Zones)
			for _, zone := range region.Zones {
				totalSubnets += len(zone.Subnets)
				for _, subnet := range zone.Subnets {
					for _, deviceMac := range subnet.Devices {
						assignedDevices[deviceMac] = true
					}
				}
			}
		}
	}

	stats["totalRegions"] = totalRegions
	stats["totalZones"] = totalZones
	stats["totalSubnets"] = totalSubnets
	stats["assignedDevices"] = len(assignedDevices)
	stats["unassignedDevices"] = len(QC.DevData) - len(assignedDevices)

	return stats
}
