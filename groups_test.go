package mnms

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/go-chi/chi/v5"
	"github.com/stretchr/testify/assert"
)

func TestGroupValidation(t *testing.T) {
	// Test valid relm
	validRelm := &Relm{
		Name:     "test-relm",
		Comment:  "Test relm",
		Location: "Test location",
	}
	err := ValidateRelm(validRelm)
	assert.NoError(t, err)

	// Test invalid relm name (empty)
	invalidRelm := &Relm{
		Name: "",
	}
	err = ValidateRelm(invalidRelm)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "relm name cannot be empty")

	// Test invalid relm name (special characters)
	invalidRelm.Name = "test@relm"
	err = ValidateRelm(invalidRelm)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "must contain only alphanumeric characters")

	// Test duplicate region names
	duplicateRegionRelm := &Relm{
		Name: "test-relm",
		Regions: []Region{
			{Name: "region1"},
			{Name: "region1"}, // duplicate
		},
	}
	err = ValidateRelm(duplicateRegionRelm)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "duplicate region name")
}

func TestMacAddressValidation(t *testing.T) {
	// Valid MAC addresses
	validMacs := []string{
		"00-60-E9-2D-91-3E",
		"AA-BB-CC-DD-EE-FF",
		"12-34-56-78-9A-BC",
	}

	for _, mac := range validMacs {
		assert.True(t, isValidMacAddress(mac), "MAC %s should be valid", mac)
	}

	// Invalid MAC addresses
	invalidMacs := []string{
		"00:60:E9:2D:91:3E",    // wrong separator
		"00-60-E9-2D-91",       // too short
		"00-60-E9-2D-91-3E-FF", // too long
		"GG-60-E9-2D-91-3E",    // invalid hex
		"",                     // empty
	}

	for _, mac := range invalidMacs {
		assert.False(t, isValidMacAddress(mac), "MAC %s should be invalid", mac)
	}
}

func TestNameValidation(t *testing.T) {
	// Valid names
	validNames := []string{
		"test",
		"test-name",
		"test_name",
		"Test123",
		"a",
		"test-123_ABC",
	}

	for _, name := range validNames {
		assert.True(t, isValidName(name), "Name %s should be valid", name)
	}

	// Invalid names
	invalidNames := []string{
		"",           // empty
		"test@name",  // special character
		"test name",  // space
		"test.name",  // dot
		"test/name",  // slash
		"test name!", // exclamation
	}

	for _, name := range invalidNames {
		assert.False(t, isValidName(name), "Name %s should be invalid", name)
	}

	// Test name length limit
	longName := ""
	for i := 0; i < 65; i++ {
		longName += "a"
	}
	assert.False(t, isValidName(longName), "Name longer than 64 characters should be invalid")
}

func TestGroupManagerOperations(t *testing.T) {
	// Initialize test environment
	QC.GroupData = make(map[string]*Relm)
	gm := &GroupManager{}

	// Test creating a relm
	testRelm := &Relm{
		Name:     "test-relm",
		Comment:  "Test relm for unit testing",
		Location: "Test location",
		Regions:  []Region{},
	}

	err := gm.CreateRelm(testRelm)
	assert.NoError(t, err)

	// Test getting the relm
	retrievedRelm, err := gm.GetRelm("test-relm")
	assert.NoError(t, err)
	assert.Equal(t, testRelm.Name, retrievedRelm.Name)
	assert.Equal(t, testRelm.Comment, retrievedRelm.Comment)

	// Test creating duplicate relm
	err = gm.CreateRelm(testRelm)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "already exists")

	// Test adding a region
	testRegion := Region{
		Name:    "test-region",
		Comment: "Test region",
		Zones:   []Zone{},
	}

	err = gm.AddRegionToRelm("test-relm", testRegion)
	assert.NoError(t, err)

	// Verify region was added
	updatedRelm, err := gm.GetRelm("test-relm")
	assert.NoError(t, err)
	assert.Len(t, updatedRelm.Regions, 1)
	assert.Equal(t, "test-region", updatedRelm.Regions[0].Name)

	// Test adding a zone
	testZone := Zone{
		Name:    "test-zone",
		Comment: "Test zone",
		NmsSvc:  "test-nms",
		Subnets: []Subnet{},
	}

	err = gm.AddZoneToRegion("test-relm", "test-region", testZone)
	assert.NoError(t, err)

	// Test adding a subnet
	testSubnet := Subnet{
		Name:    "test-subnet",
		Comment: "Test subnet",
		Devices: []string{},
	}

	err = gm.AddSubnetToZone("test-relm", "test-region", "test-zone", testSubnet)
	assert.NoError(t, err)

	// Test adding a device
	testDeviceMac := "00-60-E9-2D-91-3E"
	err = gm.AddDeviceToSubnet("test-relm", "test-region", "test-zone", "test-subnet", testDeviceMac)
	assert.NoError(t, err)

	// Verify device was added
	finalRelm, err := gm.GetRelm("test-relm")
	assert.NoError(t, err)
	devices := finalRelm.Regions[0].Zones[0].Subnets[0].Devices
	assert.Len(t, devices, 1)
	assert.Equal(t, testDeviceMac, devices[0])

	// Test removing the device
	err = gm.RemoveDeviceFromSubnet("test-relm", "test-region", "test-zone", "test-subnet", testDeviceMac)
	assert.NoError(t, err)

	// Verify device was removed
	finalRelm, err = gm.GetRelm("test-relm")
	assert.NoError(t, err)
	devices = finalRelm.Regions[0].Zones[0].Subnets[0].Devices
	assert.Len(t, devices, 0)

	// Test deleting the relm
	err = gm.DeleteRelm("test-relm")
	assert.NoError(t, err)

	// Verify relm was deleted
	_, err = gm.GetRelm("test-relm")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not found")
}

func TestGroupManagerErrorCases(t *testing.T) {
	// Initialize test environment
	QC.GroupData = make(map[string]*Relm)
	gm := &GroupManager{}

	// Test operations on non-existent relm
	_, err := gm.GetRelm("non-existent")
	assert.Error(t, err)

	err = gm.DeleteRelm("non-existent")
	assert.Error(t, err)

	// Test adding region to non-existent relm
	testRegion := Region{Name: "test-region"}
	err = gm.AddRegionToRelm("non-existent", testRegion)
	assert.Error(t, err)

	// Create a relm for further testing
	testRelm := &Relm{
		Name:    "test-relm",
		Regions: []Region{},
	}
	err = gm.CreateRelm(testRelm)
	assert.NoError(t, err)

	// Test adding zone to non-existent region
	testZone := Zone{Name: "test-zone"}
	err = gm.AddZoneToRegion("test-relm", "non-existent-region", testZone)
	assert.Error(t, err)

	// Test adding duplicate region
	err = gm.AddRegionToRelm("test-relm", testRegion)
	assert.NoError(t, err)
	err = gm.AddRegionToRelm("test-relm", testRegion)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "already exists")
}

func TestFindDeviceInGroups(t *testing.T) {
	// Initialize test environment
	QC.GroupData = make(map[string]*Relm)
	gm := &GroupManager{}

	// Create test structure
	testRelm := &Relm{
		Name: "test-relm",
		Regions: []Region{
			{
				Name: "test-region",
				Zones: []Zone{
					{
						Name: "test-zone",
						Subnets: []Subnet{
							{
								Name:    "test-subnet",
								Devices: []string{"00-60-E9-2D-91-3E", "00-60-E9-2D-91-3F"},
							},
						},
					},
				},
			},
		},
	}

	err := gm.CreateRelm(testRelm)
	assert.NoError(t, err)

	// Test finding existing device
	relmName, regionName, zoneName, subnetName, err := gm.FindDeviceInGroups("00-60-E9-2D-91-3E")
	assert.NoError(t, err)
	assert.Equal(t, "test-relm", relmName)
	assert.Equal(t, "test-region", regionName)
	assert.Equal(t, "test-zone", zoneName)
	assert.Equal(t, "test-subnet", subnetName)

	// Test finding non-existent device
	_, _, _, _, err = gm.FindDeviceInGroups("00-60-E9-2D-91-99")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not found")
}

func TestGetDevicesInSubnet(t *testing.T) {
	// Initialize test environment
	QC.GroupData = make(map[string]*Relm)
	gm := &GroupManager{}

	// Create test structure
	testDevices := []string{"00-60-E9-2D-91-3E", "00-60-E9-2D-91-3F"}
	testRelm := &Relm{
		Name: "test-relm",
		Regions: []Region{
			{
				Name: "test-region",
				Zones: []Zone{
					{
						Name: "test-zone",
						Subnets: []Subnet{
							{
								Name:    "test-subnet",
								Devices: testDevices,
							},
						},
					},
				},
			},
		},
	}

	err := gm.CreateRelm(testRelm)
	assert.NoError(t, err)

	// Test getting devices from existing subnet
	devices, err := gm.GetDevicesInSubnet("test-relm", "test-region", "test-zone", "test-subnet")
	assert.NoError(t, err)
	assert.Equal(t, testDevices, devices)

	// Test getting devices from non-existent subnet
	_, err = gm.GetDevicesInSubnet("test-relm", "test-region", "test-zone", "non-existent")
	assert.Error(t, err)
}

func TestGroupHTTPHandlers(t *testing.T) {
	// Initialize test environment
	QC.GroupData = make(map[string]*Relm)

	// Create a test router
	r := chi.NewRouter()
	r.Route("/api/v1/groups", func(r chi.Router) {
		r.Get("/", HandleListGroups)
		r.Post("/", HandleCreateGroup)
		r.Get("/{relmName}", HandleGetGroup)
		r.Put("/{relmName}", HandleUpdateGroup)
		r.Delete("/{relmName}", HandleDeleteGroup)
		r.Post("/{relmName}/regions", HandleAddRegion)
		r.Post("/{relmName}/regions/{regionName}/zones", HandleAddZone)
		r.Post("/{relmName}/regions/{regionName}/zones/{zoneName}/subnets", HandleAddSubnet)
		r.Post("/{relmName}/regions/{regionName}/zones/{zoneName}/subnets/{subnetName}/devices", HandleAddDeviceToSubnet)
		r.Delete("/{relmName}/regions/{regionName}/zones/{zoneName}/subnets/{subnetName}/devices/{deviceMac}", HandleRemoveDeviceFromSubnet)
	})

	// Test creating a group
	testRelm := Relm{
		Name:     "test-relm",
		Comment:  "Test relm",
		Location: "Test location",
	}

	jsonData, _ := json.Marshal(testRelm)
	req := httptest.NewRequest("POST", "/api/v1/groups", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	// Test getting all groups
	req = httptest.NewRequest("GET", "/api/v1/groups", nil)
	w = httptest.NewRecorder()

	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	var groups map[string]*Relm
	err := json.Unmarshal(w.Body.Bytes(), &groups)
	assert.NoError(t, err)
	assert.Len(t, groups, 1)

	// Test getting a specific group
	req = httptest.NewRequest("GET", "/api/v1/groups/test-relm", nil)
	w = httptest.NewRecorder()

	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	var retrievedRelm Relm
	err = json.Unmarshal(w.Body.Bytes(), &retrievedRelm)
	assert.NoError(t, err)
	assert.Equal(t, "test-relm", retrievedRelm.Name)

	// Test adding a region
	testRegion := Region{
		Name:    "test-region",
		Comment: "Test region",
	}

	jsonData, _ = json.Marshal(testRegion)
	req = httptest.NewRequest("POST", "/api/v1/groups/test-relm/regions", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w = httptest.NewRecorder()

	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	// Test adding a zone
	testZone := Zone{
		Name:    "test-zone",
		Comment: "Test zone",
		NmsSvc:  "test-nms",
	}

	jsonData, _ = json.Marshal(testZone)
	req = httptest.NewRequest("POST", "/api/v1/groups/test-relm/regions/test-region/zones", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w = httptest.NewRecorder()

	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	// Test adding a subnet
	testSubnet := Subnet{
		Name:    "test-subnet",
		Comment: "Test subnet",
		Devices: []string{},
	}

	jsonData, _ = json.Marshal(testSubnet)
	req = httptest.NewRequest("POST", "/api/v1/groups/test-relm/regions/test-region/zones/test-zone/subnets", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w = httptest.NewRecorder()

	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	// Test adding a device
	deviceRequest := map[string]string{"deviceMac": "00-60-E9-2D-91-3E"}
	jsonData, _ = json.Marshal(deviceRequest)
	req = httptest.NewRequest("POST", "/api/v1/groups/test-relm/regions/test-region/zones/test-zone/subnets/test-subnet/devices", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w = httptest.NewRecorder()

	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	// Test removing a device
	req = httptest.NewRequest("DELETE", "/api/v1/groups/test-relm/regions/test-region/zones/test-zone/subnets/test-subnet/devices/00-60-E9-2D-91-3E", nil)
	w = httptest.NewRecorder()

	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	// Test deleting the group
	req = httptest.NewRequest("DELETE", "/api/v1/groups/test-relm", nil)
	w = httptest.NewRecorder()

	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
}

func TestGroupHTTPErrorCases(t *testing.T) {
	// Initialize test environment
	QC.GroupData = make(map[string]*Relm)

	// Create a test router
	r := chi.NewRouter()
	r.Route("/api/v1/groups", func(r chi.Router) {
		r.Get("/{relmName}", HandleGetGroup)
		r.Post("/", HandleCreateGroup)
		r.Delete("/{relmName}", HandleDeleteGroup)
	})

	// Test getting non-existent group
	req := httptest.NewRequest("GET", "/api/v1/groups/non-existent", nil)
	w := httptest.NewRecorder()

	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusInternalServerError, w.Code)

	// Test creating group with invalid data
	invalidData := `{"name": ""}`
	req = httptest.NewRequest("POST", "/api/v1/groups", bytes.NewBufferString(invalidData))
	req.Header.Set("Content-Type", "application/json")
	w = httptest.NewRecorder()

	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusInternalServerError, w.Code)

	// Test creating group with invalid JSON
	req = httptest.NewRequest("POST", "/api/v1/groups", bytes.NewBufferString("invalid json"))
	req.Header.Set("Content-Type", "application/json")
	w = httptest.NewRecorder()

	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusInternalServerError, w.Code)

	// Test deleting non-existent group
	req = httptest.NewRequest("DELETE", "/api/v1/groups/non-existent", nil)
	w = httptest.NewRecorder()

	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestGetDeviceSubnetName(t *testing.T) {
	// Initialize test data
	QC.GroupData = make(map[string]*Relm)

	// Create test group structure
	testRelm := &Relm{
		Name: "test-relm",
		Regions: []Region{
			{
				Name: "test-region",
				Zones: []Zone{
					{
						Name: "test-zone",
						Subnets: []Subnet{
							{
								Name:    "test-subnet",
								Devices: []string{"AA-BB-CC-DD-EE-FF", "11-22-33-44-55-66"},
							},
						},
					},
				},
			},
		},
	}

	QC.GroupData["test-relm"] = testRelm

	// Test GetDeviceSubnetName
	gm := &GroupManager{}

	// Device in subnet should return subnet name
	subnetName := gm.GetDeviceSubnetName("AA-BB-CC-DD-EE-FF")
	assert.Equal(t, "test-subnet", subnetName)

	// Device in subnet should return subnet name
	subnetName = gm.GetDeviceSubnetName("11-22-33-44-55-66")
	assert.Equal(t, "test-subnet", subnetName)

	// Device not in any subnet should return empty string
	subnetName = gm.GetDeviceSubnetName("99-88-77-66-55-44")
	assert.Equal(t, "", subnetName)
}

func TestDeleteOperations(t *testing.T) {
	// Initialize test data
	QC.GroupData = make(map[string]*Relm)

	// Create test group structure
	testRelm := &Relm{
		Name: "test-relm",
		Regions: []Region{
			{
				Name: "test-region",
				Zones: []Zone{
					{
						Name: "test-zone",
						Subnets: []Subnet{
							{
								Name:    "test-subnet",
								Devices: []string{"AA-BB-CC-DD-EE-FF"},
							},
						},
					},
				},
			},
		},
	}

	QC.GroupData["test-relm"] = testRelm
	gm := &GroupManager{}

	// Test DeleteSubnet
	err := gm.DeleteSubnet("test-relm", "test-region", "test-zone", "test-subnet")
	assert.NoError(t, err)

	// Verify subnet is deleted
	relm := QC.GroupData["test-relm"]
	assert.Equal(t, 0, len(relm.Regions[0].Zones[0].Subnets))

	// Test DeleteZone
	err = gm.DeleteZone("test-relm", "test-region", "test-zone")
	assert.NoError(t, err)

	// Verify zone is deleted
	assert.Equal(t, 0, len(relm.Regions[0].Zones))

	// Test DeleteRegion
	err = gm.DeleteRegion("test-relm", "test-region")
	assert.NoError(t, err)

	// Verify region is deleted
	assert.Equal(t, 0, len(relm.Regions))

	// Test error cases
	err = gm.DeleteRegion("non-existent", "test-region")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "relm 'non-existent' not found")
}
