import defaultDeviceImage from "../../assets/images/default-device.png";

const TopologyImage = (modelName) => {
  const defaultImage = window.location.origin + defaultDeviceImage;
  if (!modelName) return defaultImage;

  try {
    const url = new URL(
      `https://nimbl.blackbeartechhive.com/api/v1/files/device-images/${modelName}.png`
    ).href;

    const img = new Image();
    img.src = url;

    return img.complete && img.naturalWidth !== 0 ? url : defaultImage; // Return the URL if the image loads successfully
  } catch (error) {
    console.error("Error creating device image URL:", error);
    return defaultImage;
  }
};

export { TopologyImage };
