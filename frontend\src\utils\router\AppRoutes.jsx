import { Navigate, useRoutes } from "react-router-dom";
import MainLayout from "../../layout/Mainlayout";
import PrivateRoute from "./PrivateRoute";
import DashboardPage from "../../pages/dashboard/DashboardPage";
import DevicePage from "../../pages/device/DevicePage";
import UsersPage from "../../pages/userManagement/UsersPage";
import MibbrowserPage from "../../pages/mibBrowser/MibbrowserPage";
import ScriptPage from "../../pages/script/ScriptPage";
import TopologyPage from "../../pages/topology/TopologyPage";
import LogPage from "../../pages/logsPage/LogPage";
import ClusterInfoPage from "../../pages/clusterInfo/ClusterInfoPage";
import LoginPage from "../../pages/auth/LoginPage";
import PageNotFound from "../../pages/PageNotFound";
import WireguardSetting from "../../pages/wireguard/WireguardSetting";
import IdpsPage from "../../pages/dashboard/IdpsPage";
import FeaturesRouteValidator from "./FeaturesRouteValidator";
import TunnelPage from "../../pages/tunnel/TunnelPage";
import KeyStorePage from "../../pages/keystore/KeyStorePage";

const AppRoutes = () => {
  let element = useRoutes([
    {
      path: "/",
      element: (
        <PrivateRoute>
          <MainLayout />
        </PrivateRoute>
      ),
      children: [
        { index: true, element: <Navigate to="/dashboard" /> },
        {
          path: "dashboard",
          children: [
            { index: true, element: <Navigate to="/dashboard/device" /> },
            {
              path: "device",
              element: (
                <PrivateRoute>
                  <DashboardPage />
                </PrivateRoute>
              ),
            },
            {
              path: "idps",
              element: (
                <PrivateRoute>
                  <FeaturesRouteValidator feature="idps">
                    <IdpsPage />
                  </FeaturesRouteValidator>
                </PrivateRoute>
              ),
            },
          ],
        },
        {
          path: "devices",
          element: (
            <PrivateRoute>
              <DevicePage />
            </PrivateRoute>
          ),
        },
        {
          path: "usermanagement",
          element: (
            <PrivateRoute>
              <UsersPage />
            </PrivateRoute>
          ),
        },
        {
          path: "mibbrowser",
          element: (
            <PrivateRoute>
              <MibbrowserPage />
            </PrivateRoute>
          ),
        },
        {
          path: "scripts",
          element: (
            <PrivateRoute>
              <ScriptPage />
            </PrivateRoute>
          ),
        },
        {
          path: "topology",
          element: (
            <PrivateRoute>
              <TopologyPage />
            </PrivateRoute>
          ),
        },
        {
          path: "eventlogs",
          element: (
            <PrivateRoute>
              <LogPage />
            </PrivateRoute>
          ),
        },
        {
          path: "clusterinfo",
          element: (
            <PrivateRoute>
              <ClusterInfoPage />
            </PrivateRoute>
          ),
        },
        {
          path: "tunnels",
          element: (
            <PrivateRoute>
              <TunnelPage />
            </PrivateRoute>
          ),
        },
        {
          path: "key-store",
          element: (
            <PrivateRoute>
              <KeyStorePage />
            </PrivateRoute>
          ),
        },
        {
          path: "wireguard",
          element: (
            <PrivateRoute>
              <WireguardSetting />
            </PrivateRoute>
          ),
        },
      ],
    },
    { path: "/login", element: <LoginPage /> },
    { path: "*", element: <PageNotFound /> },
  ]);

  return element;
};

export default AppRoutes;
