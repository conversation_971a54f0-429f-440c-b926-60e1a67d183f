import { Row, Col, Badge, Flex } from "antd";
import React, { useEffect, useState } from "react";
import dayjs from "dayjs";
import { useDispatch, useSelector } from "react-redux";
import { ProTable } from "@ant-design/pro-components";
import {
  clusterInfoSelector,
  RequestClusterInfo,
} from "../../features/clusterInfo/clusterInfoSlice";
import ExportData from "../../components/exportData/ExportData";
import { useTheme } from "antd-style";
import RootClusterInfos from "../../components/clusterInfo/RootClusterInfos";

const columns = [
  {
    title: "Service Name",
    dataIndex: "name",
    key: "name",
    width: 250,
    render: (data, record) => {
      return (
        <Flex gap={10} align="center" height="100%">
          {record && record.status === "inactive" ? (
            <Badge status="error" className="cutomBadge" />
          ) : (
            <Badge color="green" className="cutomBadge" status="processing" />
          )}
          {data}
        </Flex>
      );
    },
    sorter: (a, b) => (a.name > b.name ? 1 : -1),
  },
  {
    title: "Devices",
    dataIndex: "num_devices",
    key: "num_devices",
    width: 150,
    sorter: (a, b) => (a.num_devices > b.num_devices ? 1 : -1),
  },
  {
    title: "Cmds",
    width: 100,
    dataIndex: "num_cmds",
    key: "num_cmds",
    sorter: (a, b) => (a.num_cmds > b.num_cmds ? 1 : -1),
  },
  {
    title: "Logs Received",
    dataIndex: "num_logs_received",
    key: "num_logs_received",
    width: 200,
    sorter: (a, b) => (a.num_logs_received > b.num_logs_received ? 1 : -1),
  },
  {
    title: "Logs Sent",
    dataIndex: "num_logs_sent",
    key: "num_logs_sent",
    width: 200,
    sorter: (a, b) => (a.num_logs_sent > b.num_logs_sent ? 1 : -1),
  },
  {
    title: "Start",
    dataIndex: "start",
    key: "start",
    width: 300,
    sorter: (a, b) => (a.start > b.start ? 1 : -1),
    render: (data) => {
      return dayjs(data * 1000).format("YYYY/MM/DD HH:mm:ss");
    },
  },
  {
    title: "Now",
    dataIndex: "now",
    key: "now",
    width: 300,
    render: (data) => {
      return dayjs(data * 1000).format("YYYY/MM/DD HH:mm:ss");
    },
    sorter: (a, b) => (a.Now > b.Now ? 1 : -1),
  },
  {
    title: "Go Routines",
    dataIndex: "num_goroutines",
    key: "num_goroutines",
    width: 250,
    sorter: (a, b) => (a.NumGoroutines > b.NumGoroutines ? 1 : -1),
  },
  {
    title: "IP Addresses",
    dataIndex: "ip_addresses",
    key: "ip_addresses",
    width: 350,
    render: (data) => {
      if (Array.isArray(data)) {
        return data?.join();
      }
    },
    sorter: (a, b) => (a.IPAddresses > b.IPAddresses ? 1 : -1),
  },
];

const ClusterInfoPage = () => {
  const token = useTheme();
  const { clusterInfoData, fetching } = useSelector(clusterInfoSelector);
  const [exportClusterInfoData, setExportClusterInfoData] = useState([]);
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(RequestClusterInfo());
  }, []);

  const handleRefreshClick = () => {
    dispatch(RequestClusterInfo());
  };

  useEffect(() => {
    if (clusterInfoData.length > 0) {
      const expClusterInfo = clusterInfoData.map((item) => {
        const formatedStartDate = dayjs(item.start * 1000).format(
          "YYYY/MM/DD HH:mm:ss"
        );
        const formatedNowDate = dayjs(item.now * 1000).format(
          "YYYY/MM/DD HH:mm:ss"
        );
        return {
          ...item,
          start: formatedStartDate,
          now: formatedNowDate,
        };
      });
      setExportClusterInfoData(expClusterInfo);
    }
  }, [clusterInfoData]);

  return (
    <Row gutter={[16, 16]}>
      <Col span={24}>
        <RootClusterInfos />
      </Col>
      <Col span={24}>
        <ProTable
          cardProps={{
            style: { boxShadow: token?.Card?.boxShadow },
          }}
          loading={fetching}
          headerTitle="Cluster Info"
          columns={columns}
          dataSource={clusterInfoData}
          rowKey="name"
          pagination={{
            position: ["bottomCenter"],
            showQuickJumper: true,
            size: "default",
            total: clusterInfoData.length,
            defaultPageSize: 10,
            pageSizeOptions: [10, 15, 20, 25],
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} items`,
          }}
          scroll={{
            x: 1100,
          }}
          toolbar={{
            actions: [
              <ExportData
                Columns={columns}
                DataSource={exportClusterInfoData}
                title="Cluster_Info"
              />,
            ],
          }}
          options={{
            reload: () => {
              handleRefreshClick();
            },
            fullScreen: false,
          }}
          search={false}
          dateFormatter="string"
          columnsState={{
            persistenceKey: "clusterinfo-table",
            persistenceType: "localStorage",
          }}
        />
      </Col>
    </Row>
  );
};

export default ClusterInfoPage;
