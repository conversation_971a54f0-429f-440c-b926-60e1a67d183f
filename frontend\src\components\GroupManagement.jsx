import React, { useState } from 'react';
import {
  useGetAllGroupsQuery,
  useGetGroupQuery,
  useCreateGroupMutation,
  useUpdateGroupMutation,
  useDeleteGroupMutation,
  useAddRegionMutation,
  useAddZoneMutation,
  useAddSubnetMutation,
  useAddDeviceToSubnetMutation,
  useRemoveDeviceFromSubnetMutation,
} from '../app/services/groupsApi';

const GroupManagement = () => {
  const [selectedRelm, setSelectedRelm] = useState('');
  const [newGroupData, setNewGroupData] = useState({
    name: '',
    comment: '',
    location: '',
    image: '',
  });

  // API hooks
  const { data: groups = [], isLoading, error } = useGetAllGroupsQuery();
  const { data: selectedGroup } = useGetGroupQuery(selectedRelm, {
    skip: !selectedRelm,
  });

  const [createGroup] = useCreateGroupMutation();
  const [updateGroup] = useUpdateGroupMutation();
  const [deleteGroup] = useDeleteGroupMutation();
  const [addRegion] = useAddRegionMutation();
  const [addZone] = useAddZoneMutation();
  const [addSubnet] = useAddSubnetMutation();
  const [addDeviceToSubnet] = useAddDeviceToSubnetMutation();
  const [removeDeviceFromSubnet] = useRemoveDeviceFromSubnetMutation();

  const handleCreateGroup = async (e) => {
    e.preventDefault();
    try {
      await createGroup(newGroupData).unwrap();
      setNewGroupData({ name: '', comment: '', location: '', image: '' });
      alert('Group created successfully!');
    } catch (error) {
      alert(`Error creating group: ${error.data || error.message}`);
    }
  };

  const handleDeleteGroup = async (relmName) => {
    if (window.confirm(`Are you sure you want to delete group "${relmName}"?`)) {
      try {
        await deleteGroup(relmName).unwrap();
        if (selectedRelm === relmName) {
          setSelectedRelm('');
        }
        alert('Group deleted successfully!');
      } catch (error) {
        alert(`Error deleting group: ${error.data || error.message}`);
      }
    }
  };

  const handleAddRegion = async (relmName) => {
    const regionName = prompt('Enter region name:');
    const regionComment = prompt('Enter region comment (optional):') || '';
    
    if (regionName) {
      try {
        await addRegion({
          relmName,
          regionData: {
            name: regionName,
            comment: regionComment,
            image: '',
          },
        }).unwrap();
        alert('Region added successfully!');
      } catch (error) {
        alert(`Error adding region: ${error.data || error.message}`);
      }
    }
  };

  const handleAddZone = async (relmName, regionName) => {
    const zoneName = prompt('Enter zone name:');
    const zoneComment = prompt('Enter zone comment (optional):') || '';
    const nmsSvc = prompt('Enter NMS service (optional):') || '';
    
    if (zoneName) {
      try {
        await addZone({
          relmName,
          regionName,
          zoneData: {
            name: zoneName,
            comment: zoneComment,
            nmssvc: nmsSvc,
            image: '',
          },
        }).unwrap();
        alert('Zone added successfully!');
      } catch (error) {
        alert(`Error adding zone: ${error.data || error.message}`);
      }
    }
  };

  const handleAddSubnet = async (relmName, regionName, zoneName) => {
    const subnetName = prompt('Enter subnet name:');
    const subnetComment = prompt('Enter subnet comment (optional):') || '';
    
    if (subnetName) {
      try {
        await addSubnet({
          relmName,
          regionName,
          zoneName,
          subnetData: {
            name: subnetName,
            comment: subnetComment,
            image: '',
            devices: [],
          },
        }).unwrap();
        alert('Subnet added successfully!');
      } catch (error) {
        alert(`Error adding subnet: ${error.data || error.message}`);
      }
    }
  };

  const handleAddDevice = async (relmName, regionName, zoneName, subnetName) => {
    const deviceMac = prompt('Enter device MAC address (XX-XX-XX-XX-XX-XX):');
    
    if (deviceMac) {
      try {
        await addDeviceToSubnet({
          relmName,
          regionName,
          zoneName,
          subnetName,
          deviceMac,
        }).unwrap();
        alert('Device added successfully!');
      } catch (error) {
        alert(`Error adding device: ${error.data || error.message}`);
      }
    }
  };

  const handleRemoveDevice = async (relmName, regionName, zoneName, subnetName, deviceMac) => {
    if (window.confirm(`Remove device ${deviceMac} from subnet ${subnetName}?`)) {
      try {
        await removeDeviceFromSubnet({
          relmName,
          regionName,
          zoneName,
          subnetName,
          deviceMac,
        }).unwrap();
        alert('Device removed successfully!');
      } catch (error) {
        alert(`Error removing device: ${error.data || error.message}`);
      }
    }
  };

  if (isLoading) return <div>Loading groups...</div>;
  if (error) return <div>Error loading groups: {error.message}</div>;

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>Group Management</h1>
      
      {/* Create New Group Form */}
      <div style={{ marginBottom: '30px', padding: '20px', border: '1px solid #ccc', borderRadius: '5px' }}>
        <h2>Create New Group</h2>
        <form onSubmit={handleCreateGroup}>
          <div style={{ marginBottom: '10px' }}>
            <label>Name: </label>
            <input
              type="text"
              value={newGroupData.name}
              onChange={(e) => setNewGroupData({ ...newGroupData, name: e.target.value })}
              required
              style={{ marginLeft: '10px', padding: '5px' }}
            />
          </div>
          <div style={{ marginBottom: '10px' }}>
            <label>Comment: </label>
            <input
              type="text"
              value={newGroupData.comment}
              onChange={(e) => setNewGroupData({ ...newGroupData, comment: e.target.value })}
              style={{ marginLeft: '10px', padding: '5px' }}
            />
          </div>
          <div style={{ marginBottom: '10px' }}>
            <label>Location: </label>
            <input
              type="text"
              value={newGroupData.location}
              onChange={(e) => setNewGroupData({ ...newGroupData, location: e.target.value })}
              style={{ marginLeft: '10px', padding: '5px' }}
            />
          </div>
          <button type="submit" style={{ padding: '10px 20px', backgroundColor: '#007bff', color: 'white', border: 'none', borderRadius: '3px' }}>
            Create Group
          </button>
        </form>
      </div>

      {/* Groups List */}
      <div style={{ marginBottom: '30px' }}>
        <h2>Existing Groups</h2>
        {groups.length === 0 ? (
          <p>No groups found.</p>
        ) : (
          <div>
            {groups.map((group) => (
              <div key={group.name} style={{ marginBottom: '10px', padding: '10px', border: '1px solid #ddd', borderRadius: '3px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <div>
                    <strong>{group.name}</strong>
                    {group.comment && <span> - {group.comment}</span>}
                    {group.location && <span> ({group.location})</span>}
                  </div>
                  <div>
                    <button
                      onClick={() => setSelectedRelm(group.name)}
                      style={{ marginRight: '10px', padding: '5px 10px', backgroundColor: '#28a745', color: 'white', border: 'none', borderRadius: '3px' }}
                    >
                      View Details
                    </button>
                    <button
                      onClick={() => handleAddRegion(group.name)}
                      style={{ marginRight: '10px', padding: '5px 10px', backgroundColor: '#17a2b8', color: 'white', border: 'none', borderRadius: '3px' }}
                    >
                      Add Region
                    </button>
                    <button
                      onClick={() => handleDeleteGroup(group.name)}
                      style={{ padding: '5px 10px', backgroundColor: '#dc3545', color: 'white', border: 'none', borderRadius: '3px' }}
                    >
                      Delete
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Selected Group Details */}
      {selectedGroup && (
        <div style={{ padding: '20px', border: '1px solid #ccc', borderRadius: '5px' }}>
          <h2>Group Details: {selectedGroup.name}</h2>
          <p><strong>Comment:</strong> {selectedGroup.comment || 'N/A'}</p>
          <p><strong>Location:</strong> {selectedGroup.location || 'N/A'}</p>
          <p><strong>Created:</strong> {selectedGroup.created_at}</p>
          <p><strong>Updated:</strong> {selectedGroup.updated_at}</p>
          
          <h3>Regions ({selectedGroup.regions?.length || 0})</h3>
          {selectedGroup.regions?.map((region) => (
            <div key={region.name} style={{ marginLeft: '20px', marginBottom: '15px', padding: '10px', border: '1px solid #eee', borderRadius: '3px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <strong>Region: {region.name}</strong>
                <button
                  onClick={() => handleAddZone(selectedGroup.name, region.name)}
                  style={{ padding: '3px 8px', backgroundColor: '#17a2b8', color: 'white', border: 'none', borderRadius: '3px' }}
                >
                  Add Zone
                </button>
              </div>
              {region.comment && <p style={{ margin: '5px 0', fontSize: '14px' }}>Comment: {region.comment}</p>}
              
              {region.zones?.map((zone) => (
                <div key={zone.name} style={{ marginLeft: '20px', marginTop: '10px', padding: '8px', border: '1px solid #f0f0f0', borderRadius: '3px' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <strong>Zone: {zone.name}</strong>
                    <button
                      onClick={() => handleAddSubnet(selectedGroup.name, region.name, zone.name)}
                      style={{ padding: '2px 6px', backgroundColor: '#17a2b8', color: 'white', border: 'none', borderRadius: '3px' }}
                    >
                      Add Subnet
                    </button>
                  </div>
                  {zone.comment && <p style={{ margin: '3px 0', fontSize: '12px' }}>Comment: {zone.comment}</p>}
                  {zone.nmssvc && <p style={{ margin: '3px 0', fontSize: '12px' }}>NMS Service: {zone.nmssvc}</p>}
                  
                  {zone.subnets?.map((subnet) => (
                    <div key={subnet.name} style={{ marginLeft: '20px', marginTop: '8px', padding: '6px', border: '1px solid #f8f8f8', borderRadius: '3px' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <strong>Subnet: {subnet.name}</strong>
                        <button
                          onClick={() => handleAddDevice(selectedGroup.name, region.name, zone.name, subnet.name)}
                          style={{ padding: '2px 6px', backgroundColor: '#28a745', color: 'white', border: 'none', borderRadius: '3px' }}
                        >
                          Add Device
                        </button>
                      </div>
                      {subnet.comment && <p style={{ margin: '3px 0', fontSize: '11px' }}>Comment: {subnet.comment}</p>}
                      
                      <div style={{ marginTop: '5px' }}>
                        <strong style={{ fontSize: '12px' }}>Devices ({subnet.devices?.length || 0}):</strong>
                        {subnet.devices?.length > 0 ? (
                          <ul style={{ margin: '5px 0', paddingLeft: '20px' }}>
                            {subnet.devices.map((device) => (
                              <li key={device} style={{ fontSize: '11px', marginBottom: '2px' }}>
                                {device}
                                <button
                                  onClick={() => handleRemoveDevice(selectedGroup.name, region.name, zone.name, subnet.name, device)}
                                  style={{ marginLeft: '10px', padding: '1px 4px', backgroundColor: '#dc3545', color: 'white', border: 'none', borderRadius: '2px', fontSize: '10px' }}
                                >
                                  Remove
                                </button>
                              </li>
                            ))}
                          </ul>
                        ) : (
                          <p style={{ fontSize: '11px', margin: '3px 0', fontStyle: 'italic' }}>No devices</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ))}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default GroupManagement;
