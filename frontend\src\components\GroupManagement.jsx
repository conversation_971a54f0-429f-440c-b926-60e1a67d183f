import React, { useState } from "react";
import {
  Card,
  Tree,
  Button,
  Modal,
  Form,
  Input,
  Transfer,
  Statistic,
  Row,
  Col,
  Space,
  Typography,
  Divider,
  message,
  Popconfirm,
  Badge,
  Layout,
} from "antd";
import {
  PlusOutlined,
  DeleteOutlined,
  UserOutlined,
  GlobalOutlined,
  ClusterOutlined,
  DatabaseOutlined,
  DesktopOutlined,
} from "@ant-design/icons";
import {
  useGetAllGroupsQuery,
  useGetGroupQuery,
  useCreateGroupMutation,
  useDeleteGroupMutation,
  useAddRegionMutation,
  useDeleteRegionMutation,
  useAddZoneMutation,
  useDeleteZoneMutation,
  useAddSubnetMutation,
  useDeleteSubnetMutation,
  useAddDeviceToSubnetMutation,
  useRemoveDeviceFromSubnetMutation,
  useGetDevicesForTransferQuery,
  useGetGroupStatisticsQuery,
} from "../app/services/groupsApi";

const { Title, Text } = Typography;
const { Sider, Content } = Layout;

const GroupManagement = () => {
  const [selectedRelm, setSelectedRelm] = useState("");
  const [selectedSubnet, setSelectedSubnet] = useState(null);
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isDeviceTransferVisible, setIsDeviceTransferVisible] = useState(false);
  const [isAddRegionVisible, setIsAddRegionVisible] = useState(false);
  const [isAddZoneVisible, setIsAddZoneVisible] = useState(false);
  const [isAddSubnetVisible, setIsAddSubnetVisible] = useState(false);
  const [selectedHierarchy, setSelectedHierarchy] = useState(null);
  const [transferTargetKeys, setTransferTargetKeys] = useState([]);
  const [form] = Form.useForm();
  const [regionForm] = Form.useForm();
  const [zoneForm] = Form.useForm();
  const [subnetForm] = Form.useForm();

  // API hooks
  const { data: groups = [], isLoading, error } = useGetAllGroupsQuery();
  const { data: selectedGroup } = useGetGroupQuery(selectedRelm, {
    skip: !selectedRelm,
  });
  const { data: statistics } = useGetGroupStatisticsQuery();
  const { data: devicesForTransfer = [] } = useGetDevicesForTransferQuery();

  const [createGroup] = useCreateGroupMutation();
  const [deleteGroup] = useDeleteGroupMutation();
  const [addRegion] = useAddRegionMutation();
  const [deleteRegion] = useDeleteRegionMutation();
  const [addZone] = useAddZoneMutation();
  const [deleteZone] = useDeleteZoneMutation();
  const [addSubnet] = useAddSubnetMutation();
  const [deleteSubnet] = useDeleteSubnetMutation();
  const [addDeviceToSubnet] = useAddDeviceToSubnetMutation();
  const [removeDeviceFromSubnet] = useRemoveDeviceFromSubnetMutation();

  // Generate tree data for groups
  const generateTreeData = () => {
    return groups.map((group) => ({
      title: (
        <Space>
          <GlobalOutlined />
          <Text strong>{group.name}</Text>
          <Badge count={group.regions?.length || 0} showZero />
          <Button
            type="link"
            size="small"
            icon={<PlusOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              setSelectedHierarchy({ relmName: group.name });
              setIsAddRegionVisible(true);
            }}
          >
            Add Region
          </Button>
        </Space>
      ),
      key: `relm-${group.name}`,
      children: group.regions?.map((region) => ({
        title: (
          <Space>
            <ClusterOutlined />
            <Text>{region.name}</Text>
            <Badge count={region.zones?.length || 0} showZero />
            <Button
              type="link"
              size="small"
              icon={<PlusOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                setSelectedHierarchy({
                  relmName: group.name,
                  regionName: region.name,
                });
                setIsAddZoneVisible(true);
              }}
            >
              Add Zone
            </Button>
            <Popconfirm
              title="Delete Region"
              description="Are you sure you want to delete this region and all its zones/subnets?"
              onConfirm={(e) => {
                e.stopPropagation();
                handleDeleteRegion(group.name, region.name);
              }}
              okText="Yes"
              cancelText="No"
            >
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
                onClick={(e) => e.stopPropagation()}
              >
                Delete
              </Button>
            </Popconfirm>
          </Space>
        ),
        key: `region-${group.name}-${region.name}`,
        children: region.zones?.map((zone) => ({
          title: (
            <Space>
              <DatabaseOutlined />
              <Text>{zone.name}</Text>
              <Badge count={zone.subnets?.length || 0} showZero />
              <Button
                type="link"
                size="small"
                icon={<PlusOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  setSelectedHierarchy({
                    relmName: group.name,
                    regionName: region.name,
                    zoneName: zone.name,
                  });
                  setIsAddSubnetVisible(true);
                }}
              >
                Add Subnet
              </Button>
              <Popconfirm
                title="Delete Zone"
                description="Are you sure you want to delete this zone and all its subnets?"
                onConfirm={(e) => {
                  e.stopPropagation();
                  handleDeleteZone(group.name, region.name, zone.name);
                }}
                okText="Yes"
                cancelText="No"
              >
                <Button
                  type="link"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={(e) => e.stopPropagation()}
                >
                  Delete
                </Button>
              </Popconfirm>
            </Space>
          ),
          key: `zone-${group.name}-${region.name}-${zone.name}`,
          children: zone.subnets?.map((subnet) => ({
            title: (
              <Space>
                <DesktopOutlined />
                <Text>{subnet.name}</Text>
                <Badge count={subnet.devices?.length || 0} showZero />
                <Button
                  type="link"
                  size="small"
                  icon={<PlusOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedSubnet({
                      name: subnet.name,
                      relmName: group.name,
                      regionName: region.name,
                      zoneName: zone.name,
                      devices: subnet.devices || [],
                    });
                    // Initialize transfer with current subnet devices as target keys
                    setTransferTargetKeys(subnet.devices || []);
                    setIsDeviceTransferVisible(true);
                  }}
                >
                  Add Devices
                </Button>
                <Popconfirm
                  title="Delete Subnet"
                  description="Are you sure you want to delete this subnet and remove all its devices?"
                  onConfirm={(e) => {
                    e.stopPropagation();
                    handleDeleteSubnet(
                      group.name,
                      region.name,
                      zone.name,
                      subnet.name
                    );
                  }}
                  okText="Yes"
                  cancelText="No"
                >
                  <Button
                    type="link"
                    size="small"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={(e) => e.stopPropagation()}
                  >
                    Delete
                  </Button>
                </Popconfirm>
              </Space>
            ),
            key: `subnet-${group.name}-${region.name}-${zone.name}-${subnet.name}`,
            isLeaf: true,
          })),
        })),
      })),
    }));
  };

  const handleCreateGroup = async (values) => {
    try {
      await createGroup(values).unwrap();
      message.success("Group created successfully!");
      setIsCreateModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error(`Error creating group: ${error.data || error.message}`);
    }
  };

  const handleDeleteGroup = async (relmName) => {
    try {
      await deleteGroup(relmName).unwrap();
      message.success("Group deleted successfully!");
      if (selectedRelm === relmName) {
        setSelectedRelm("");
      }
    } catch (error) {
      message.error(`Error deleting group: ${error.data || error.message}`);
    }
  };

  const handleAddRegion = async (values) => {
    if (!selectedHierarchy) return;

    try {
      // Use the name as provided by user (no prefix for regions)
      await addRegion({
        relmName: selectedHierarchy.relmName,
        regionData: values,
      }).unwrap();

      message.success("Region added successfully!");
      setIsAddRegionVisible(false);
      regionForm.resetFields();
      setSelectedHierarchy(null);
    } catch (error) {
      message.error(`Error adding region: ${error.data || error.message}`);
    }
  };

  const handleAddZone = async (values) => {
    if (!selectedHierarchy) return;

    try {
      // Create hierarchical name: region-zone
      const hierarchicalName = `${selectedHierarchy.regionName}-${values.name}`;

      await addZone({
        relmName: selectedHierarchy.relmName,
        regionName: selectedHierarchy.regionName,
        zoneData: {
          ...values,
          name: hierarchicalName,
        },
      }).unwrap();

      message.success("Zone added successfully!");
      setIsAddZoneVisible(false);
      zoneForm.resetFields();
      setSelectedHierarchy(null);
    } catch (error) {
      message.error(`Error adding zone: ${error.data || error.message}`);
    }
  };

  const handleAddSubnet = async (values) => {
    if (!selectedHierarchy) return;

    try {
      // Create hierarchical name: region-zone-subnet
      const hierarchicalName = `${selectedHierarchy.zoneName}-${values.name}`;

      await addSubnet({
        relmName: selectedHierarchy.relmName,
        regionName: selectedHierarchy.regionName,
        zoneName: selectedHierarchy.zoneName,
        subnetData: {
          ...values,
          name: hierarchicalName,
          devices: [],
        },
      }).unwrap();

      message.success("Subnet added successfully!");
      setIsAddSubnetVisible(false);
      subnetForm.resetFields();
      setSelectedHierarchy(null);
    } catch (error) {
      message.error(`Error adding subnet: ${error.data || error.message}`);
    }
  };

  const handleDeleteRegion = async (relmName, regionName) => {
    try {
      await deleteRegion({ relmName, regionName }).unwrap();
      message.success("Region deleted successfully!");
    } catch (error) {
      message.error(`Error deleting region: ${error.data || error.message}`);
    }
  };

  const handleDeleteZone = async (relmName, regionName, zoneName) => {
    try {
      await deleteZone({ relmName, regionName, zoneName }).unwrap();
      message.success("Zone deleted successfully!");
    } catch (error) {
      message.error(`Error deleting zone: ${error.data || error.message}`);
    }
  };

  const handleDeleteSubnet = async (
    relmName,
    regionName,
    zoneName,
    subnetName
  ) => {
    try {
      await deleteSubnet({
        relmName,
        regionName,
        zoneName,
        subnetName,
      }).unwrap();
      message.success("Subnet deleted successfully!");
    } catch (error) {
      message.error(`Error deleting subnet: ${error.data || error.message}`);
    }
  };

  const handleDeviceTransfer = async () => {
    if (!selectedSubnet) return;

    try {
      // Remove devices that are being moved from left to right (removed from subnet)
      const devicesToRemove = selectedSubnet.devices.filter(
        (device) => !transferTargetKeys.includes(device)
      );

      // Add devices that are being moved from right to left (added to subnet)
      const devicesToAdd = transferTargetKeys.filter(
        (device) => !selectedSubnet.devices.includes(device)
      );

      // Remove devices from subnet
      for (const deviceMac of devicesToRemove) {
        await removeDeviceFromSubnet({
          relmName: selectedSubnet.relmName,
          regionName: selectedSubnet.regionName,
          zoneName: selectedSubnet.zoneName,
          subnetName: selectedSubnet.name,
          deviceMac,
        }).unwrap();
      }

      // Add devices to subnet
      for (const deviceMac of devicesToAdd) {
        await addDeviceToSubnet({
          relmName: selectedSubnet.relmName,
          regionName: selectedSubnet.regionName,
          zoneName: selectedSubnet.zoneName,
          subnetName: selectedSubnet.name,
          deviceMac,
        }).unwrap();
      }

      message.success(`Updated subnet devices successfully`);
      setIsDeviceTransferVisible(false);
      setTransferTargetKeys([]);
      setSelectedSubnet(null);
    } catch (error) {
      message.error(`Error updating devices: ${error.data || error.message}`);
    }
  };

  if (isLoading) return <div>Loading groups...</div>;
  if (error) return <div>Error loading groups: {error.message}</div>;

  return (
    <Layout style={{ minHeight: "100vh", padding: "24px" }}>
      <Row gutter={[16, 16]}>
        {/* Statistics Cards */}
        <Col span={24}>
          <Row gutter={16}>
            <Col span={6}>
              <Card>
                <Statistic
                  title="Total Groups"
                  value={statistics?.totalRelms || 0}
                  prefix={<GlobalOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="Total Devices"
                  value={statistics?.totalDevices || 0}
                  prefix={<DesktopOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="Assigned Devices"
                  value={statistics?.assignedDevices || 0}
                  prefix={<UserOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="Unassigned Devices"
                  value={statistics?.unassignedDevices || 0}
                  prefix={<UserOutlined />}
                />
              </Card>
            </Col>
          </Row>
        </Col>

        {/* Main Content */}
        <Col span={24}>
          <Layout>
            <Sider width={600} style={{ background: "#fff", padding: "16px" }}>
              <Card
                title="Groups Hierarchy"
                extra={
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => setIsCreateModalVisible(true)}
                  >
                    Create Group
                  </Button>
                }
              >
                <Tree
                  showIcon={false}
                  defaultExpandAll
                  treeData={generateTreeData()}
                  onSelect={(selectedKeys) => {
                    const key = selectedKeys[0];
                    if (key && key.startsWith("relm-")) {
                      const relmName = key.replace("relm-", "");
                      setSelectedRelm(relmName);
                    }
                  }}
                />
              </Card>
            </Sider>

            <Content style={{ padding: "0 16px" }}>
              {selectedGroup ? (
                <Card
                  title={`Group Details: ${selectedGroup.name}`}
                  extra={
                    <Popconfirm
                      title="Are you sure you want to delete this group?"
                      onConfirm={() => handleDeleteGroup(selectedGroup.name)}
                      okText="Yes"
                      cancelText="No"
                    >
                      <Button danger icon={<DeleteOutlined />}>
                        Delete Group
                      </Button>
                    </Popconfirm>
                  }
                >
                  <Space direction="vertical" style={{ width: "100%" }}>
                    <Text>
                      <strong>Comment:</strong> {selectedGroup.comment || "N/A"}
                    </Text>
                    <Text>
                      <strong>Location:</strong>{" "}
                      {selectedGroup.location || "N/A"}
                    </Text>
                    <Text>
                      <strong>Created:</strong> {selectedGroup.created_at}
                    </Text>
                    <Text>
                      <strong>Updated:</strong> {selectedGroup.updated_at}
                    </Text>

                    <Divider />

                    <Title level={4}>
                      Regions ({selectedGroup.regions?.length || 0})
                    </Title>

                    {selectedGroup.regions?.length > 0 ? (
                      selectedGroup.regions.map((region) => (
                        <Card
                          key={region.name}
                          size="small"
                          style={{ marginBottom: "16px" }}
                          title={
                            <Space>
                              <ClusterOutlined />
                              <Text strong>{region.name}</Text>
                              <Badge
                                count={region.zones?.length || 0}
                                showZero
                              />
                            </Space>
                          }
                        >
                          {region.comment && (
                            <Text
                              type="secondary"
                              style={{ display: "block", marginBottom: "8px" }}
                            >
                              {region.comment}
                            </Text>
                          )}

                          <Title level={5}>
                            Zones ({region.zones?.length || 0})
                          </Title>
                          {region.zones?.length > 0 ? (
                            region.zones.map((zone) => (
                              <Card
                                key={zone.name}
                                size="small"
                                style={{
                                  marginBottom: "12px",
                                  marginLeft: "16px",
                                }}
                                title={
                                  <Space>
                                    <DatabaseOutlined />
                                    <Text>{zone.name}</Text>
                                    <Badge
                                      count={zone.subnets?.length || 0}
                                      showZero
                                    />
                                  </Space>
                                }
                              >
                                {zone.comment && (
                                  <Text
                                    type="secondary"
                                    style={{
                                      display: "block",
                                      marginBottom: "8px",
                                    }}
                                  >
                                    {zone.comment}
                                  </Text>
                                )}
                                {zone.nmssvc && (
                                  <Text
                                    type="secondary"
                                    style={{
                                      display: "block",
                                      marginBottom: "8px",
                                    }}
                                  >
                                    NMS Service: {zone.nmssvc}
                                  </Text>
                                )}

                                <Title level={5}>
                                  Subnets ({zone.subnets?.length || 0})
                                </Title>
                                {zone.subnets?.length > 0 ? (
                                  zone.subnets.map((subnet) => (
                                    <Card
                                      key={subnet.name}
                                      size="small"
                                      style={{
                                        marginBottom: "8px",
                                        marginLeft: "16px",
                                      }}
                                      title={
                                        <Space>
                                          <DesktopOutlined />
                                          <Text>{subnet.name}</Text>
                                          <Badge
                                            count={subnet.devices?.length || 0}
                                            showZero
                                          />
                                        </Space>
                                      }
                                    >
                                      {subnet.comment && (
                                        <Text
                                          type="secondary"
                                          style={{
                                            display: "block",
                                            marginBottom: "8px",
                                          }}
                                        >
                                          {subnet.comment}
                                        </Text>
                                      )}

                                      <Title level={5}>
                                        Devices ({subnet.devices?.length || 0})
                                      </Title>
                                      {subnet.devices?.length > 0 ? (
                                        <div
                                          style={{
                                            maxHeight: "200px",
                                            overflowY: "auto",
                                          }}
                                        >
                                          {subnet.devices.map((deviceMac) => (
                                            <div
                                              key={deviceMac}
                                              style={{
                                                padding: "4px 8px",
                                                margin: "2px 0",
                                                backgroundColor: "#f5f5f5",
                                                borderRadius: "4px",
                                                fontSize: "12px",
                                              }}
                                            >
                                              {deviceMac}
                                            </div>
                                          ))}
                                        </div>
                                      ) : (
                                        <Text
                                          type="secondary"
                                          style={{ fontStyle: "italic" }}
                                        >
                                          No devices assigned
                                        </Text>
                                      )}
                                    </Card>
                                  ))
                                ) : (
                                  <Text
                                    type="secondary"
                                    style={{ fontStyle: "italic" }}
                                  >
                                    No subnets created
                                  </Text>
                                )}
                              </Card>
                            ))
                          ) : (
                            <Text
                              type="secondary"
                              style={{ fontStyle: "italic" }}
                            >
                              No zones created
                            </Text>
                          )}
                        </Card>
                      ))
                    ) : (
                      <Text type="secondary" style={{ fontStyle: "italic" }}>
                        No regions created. Click "Add Region" to get started.
                      </Text>
                    )}
                  </Space>
                </Card>
              ) : (
                <Card>
                  <div style={{ textAlign: "center", padding: "50px" }}>
                    <GlobalOutlined
                      style={{ fontSize: "48px", color: "#ccc" }}
                    />
                    <Title level={3} style={{ color: "#ccc" }}>
                      Select a group to view details
                    </Title>
                  </div>
                </Card>
              )}
            </Content>
          </Layout>
        </Col>
      </Row>

      {/* Create Group Modal */}
      <Modal
        title="Create New Group"
        open={isCreateModalVisible}
        onCancel={() => {
          setIsCreateModalVisible(false);
          form.resetFields();
        }}
        footer={null}
      >
        <Form form={form} onFinish={handleCreateGroup} layout="vertical">
          <Form.Item
            name="name"
            label="Group Name"
            rules={[{ required: true, message: "Please enter group name" }]}
          >
            <Input placeholder="Enter group name" />
          </Form.Item>
          <Form.Item name="comment" label="Comment">
            <Input.TextArea placeholder="Enter comment (optional)" />
          </Form.Item>
          <Form.Item name="location" label="Location">
            <Input placeholder="Enter location (optional)" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                Create Group
              </Button>
              <Button
                onClick={() => {
                  setIsCreateModalVisible(false);
                  form.resetFields();
                }}
              >
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Device Transfer Modal */}
      <Modal
        title={`Manage Devices for ${selectedSubnet?.name || "Subnet"}`}
        open={isDeviceTransferVisible}
        onCancel={() => {
          setIsDeviceTransferVisible(false);
          setTransferTargetKeys([]);
          setSelectedSubnet(null);
        }}
        onOk={handleDeviceTransfer}
        width="60%"
        style={{ top: 20 }}
      >
        <div style={{ height: "500px" }}>
          <Transfer
            dataSource={devicesForTransfer}
            targetKeys={transferTargetKeys}
            onChange={setTransferTargetKeys}
            render={(item) => (
              <div>
                <div style={{ fontWeight: "bold" }}>{item.title}</div>
                <div style={{ fontSize: "12px", color: "#666" }}>
                  {item.description}
                </div>
              </div>
            )}
            titles={["Available Devices", "Assigned Devices"]}
            showSearch
            listStyle={{
              width: "50%",
              height: "450px",
            }}
            filterOption={(inputValue, option) =>
              option.title.toLowerCase().includes(inputValue.toLowerCase()) ||
              option.description
                .toLowerCase()
                .includes(inputValue.toLowerCase())
            }
          />
        </div>
      </Modal>

      {/* Add Region Modal */}
      <Modal
        title="Add Region"
        open={isAddRegionVisible}
        onCancel={() => {
          setIsAddRegionVisible(false);
          regionForm.resetFields();
          setSelectedHierarchy(null);
        }}
        footer={null}
      >
        <Form form={regionForm} onFinish={handleAddRegion} layout="vertical">
          <Form.Item
            name="name"
            label="Region Name"
            rules={[{ required: true, message: "Please enter region name" }]}
          >
            <Input placeholder="Enter region name" />
          </Form.Item>
          <Form.Item name="comment" label="Comment">
            <Input.TextArea placeholder="Enter comment (optional)" />
          </Form.Item>
          <Form.Item name="image" label="Image">
            <Input placeholder="Enter image URL or base64 (optional)" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                Add Region
              </Button>
              <Button
                onClick={() => {
                  setIsAddRegionVisible(false);
                  regionForm.resetFields();
                  setSelectedHierarchy(null);
                }}
              >
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Add Zone Modal */}
      <Modal
        title="Add Zone"
        open={isAddZoneVisible}
        onCancel={() => {
          setIsAddZoneVisible(false);
          zoneForm.resetFields();
          setSelectedHierarchy(null);
        }}
        footer={null}
      >
        <Form form={zoneForm} onFinish={handleAddZone} layout="vertical">
          <Form.Item
            name="name"
            label="Zone Name"
            rules={[{ required: true, message: "Please enter zone name" }]}
            extra={`Final name will be: ${
              selectedHierarchy?.regionName || "region"
            }-[your input]`}
          >
            <Input placeholder="Enter zone name (will be prefixed with region name)" />
          </Form.Item>
          <Form.Item name="comment" label="Comment">
            <Input.TextArea placeholder="Enter comment (optional)" />
          </Form.Item>
          <Form.Item name="nmssvc" label="NMS Service">
            <Input placeholder="Enter NMS service name (optional)" />
          </Form.Item>
          <Form.Item name="image" label="Image">
            <Input placeholder="Enter image URL or base64 (optional)" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                Add Zone
              </Button>
              <Button
                onClick={() => {
                  setIsAddZoneVisible(false);
                  zoneForm.resetFields();
                  setSelectedHierarchy(null);
                }}
              >
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Add Subnet Modal */}
      <Modal
        title="Add Subnet"
        open={isAddSubnetVisible}
        onCancel={() => {
          setIsAddSubnetVisible(false);
          subnetForm.resetFields();
          setSelectedHierarchy(null);
        }}
        footer={null}
      >
        <Form form={subnetForm} onFinish={handleAddSubnet} layout="vertical">
          <Form.Item
            name="name"
            label="Subnet Name"
            rules={[{ required: true, message: "Please enter subnet name" }]}
            extra={`Final name will be: ${
              selectedHierarchy?.zoneName || "zone"
            }-[your input]`}
          >
            <Input placeholder="Enter subnet name (will be prefixed with zone name)" />
          </Form.Item>
          <Form.Item name="comment" label="Comment">
            <Input.TextArea placeholder="Enter comment (optional)" />
          </Form.Item>
          <Form.Item name="image" label="Image">
            <Input placeholder="Enter image URL or base64 (optional)" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                Add Subnet
              </Button>
              <Button
                onClick={() => {
                  setIsAddSubnetVisible(false);
                  subnetForm.resetFields();
                  setSelectedHierarchy(null);
                }}
              >
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </Layout>
  );
};

export default GroupManagement;
