import { api } from "./api";

export const groupsApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get all groups (relms)
    getAllGroups: builder.query({
      query: () => "api/v1/groups",
      providesTags: ["groups"],
      transformResponse: (resp) => {
        // Convert object to array for easier handling
        return Object.values(resp);
      },
    }),

    // Get a specific group by name
    getGroup: builder.query({
      query: (relmName) => `api/v1/groups/${relmName}`,
      providesTags: (result, error, relmName) => [
        { type: "groups", id: relmName },
      ],
    }),

    // Create a new group (relm)
    createGroup: builder.mutation({
      query: (groupData) => ({
        url: "api/v1/groups",
        method: "POST",
        body: groupData,
      }),
      invalidatesTags: ["groups"],
    }),

    // Update an existing group
    updateGroup: builder.mutation({
      query: ({ relmName, ...groupData }) => ({
        url: `api/v1/groups/${relmName}`,
        method: "PUT",
        body: groupData,
      }),
      invalidatesTags: (result, error, { relmName }) => [
        "groups",
        { type: "groups", id: relmName },
      ],
    }),

    // Delete a group
    deleteGroup: builder.mutation({
      query: (relmName) => ({
        url: `api/v1/groups/${relmName}`,
        method: "DELETE",
      }),
      invalidatesTags: ["groups"],
    }),

    // Add a region to a relm
    addRegion: builder.mutation({
      query: ({ relmName, regionData }) => ({
        url: `api/v1/groups/${relmName}/regions`,
        method: "POST",
        body: regionData,
      }),
      invalidatesTags: (result, error, { relmName }) => [
        "groups",
        { type: "groups", id: relmName },
      ],
    }),

    // Add a zone to a region
    addZone: builder.mutation({
      query: ({ relmName, regionName, zoneData }) => ({
        url: `api/v1/groups/${relmName}/regions/${regionName}/zones`,
        method: "POST",
        body: zoneData,
      }),
      invalidatesTags: (result, error, { relmName }) => [
        "groups",
        { type: "groups", id: relmName },
      ],
    }),

    // Add a subnet to a zone
    addSubnet: builder.mutation({
      query: ({ relmName, regionName, zoneName, subnetData }) => ({
        url: `api/v1/groups/${relmName}/regions/${regionName}/zones/${zoneName}/subnets`,
        method: "POST",
        body: subnetData,
      }),
      invalidatesTags: (result, error, { relmName }) => [
        "groups",
        { type: "groups", id: relmName },
      ],
    }),

    // Add a device to a subnet
    addDeviceToSubnet: builder.mutation({
      query: ({ relmName, regionName, zoneName, subnetName, deviceMac }) => ({
        url: `api/v1/groups/${relmName}/regions/${regionName}/zones/${zoneName}/subnets/${subnetName}/devices`,
        method: "POST",
        body: { deviceMac },
      }),
      invalidatesTags: (result, error, { relmName }) => [
        "groups",
        { type: "groups", id: relmName },
      ],
    }),

    // Remove a device from a subnet
    removeDeviceFromSubnet: builder.mutation({
      query: ({ relmName, regionName, zoneName, subnetName, deviceMac }) => ({
        url: `api/v1/groups/${relmName}/regions/${regionName}/zones/${zoneName}/subnets/${subnetName}/devices/${deviceMac}`,
        method: "DELETE",
      }),
      invalidatesTags: (result, error, { relmName }) => [
        "groups",
        { type: "groups", id: relmName },
      ],
    }),

    // Get unassigned devices
    getUnassignedDevices: builder.query({
      query: () => "api/v1/groups/devices/unassigned",
      providesTags: ["groups"],
    }),

    // Get devices formatted for Transfer component
    getDevicesForTransfer: builder.query({
      query: () => "api/v1/groups/devices/transfer",
      providesTags: ["groups"],
    }),

    // Get available devices for a subnet
    getAvailableDevicesForSubnet: builder.query({
      query: (subnetName) => `api/v1/groups/devices/available/${subnetName}`,
      providesTags: ["groups"],
    }),

    // Get group statistics
    getGroupStatistics: builder.query({
      query: () => "api/v1/groups/statistics",
      providesTags: ["groups"],
    }),
  }),
});

export const {
  useGetAllGroupsQuery,
  useGetGroupQuery,
  useCreateGroupMutation,
  useUpdateGroupMutation,
  useDeleteGroupMutation,
  useAddRegionMutation,
  useAddZoneMutation,
  useAddSubnetMutation,
  useAddDeviceToSubnetMutation,
  useRemoveDeviceFromSubnetMutation,
  useGetUnassignedDevicesQuery,
  useGetDevicesForTransferQuery,
  useGetAvailableDevicesForSubnetQuery,
  useGetGroupStatisticsQuery,
} = groupsApi;
